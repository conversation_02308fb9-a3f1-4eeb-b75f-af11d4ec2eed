#!/usr/bin/env python3
"""
简单测试OpenRouter认证
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MCPClient

async def test_openrouter_auth():
    """测试OpenRouter认证"""
    print("🔐 OpenRouter 认证测试")
    print("=" * 40)
    
    # 1. 初始化配置管理器
    print("📋 初始化配置管理器...")
    config_manager = ConfigManager()
    config_manager.load_config()
    
    # 2. 检查环境变量
    print(f"🌍 检查环境变量:")
    openrouter_key = os.getenv('OPENROUTER_API_KEY')
    if openrouter_key:
        print(f"  ✅ OPENROUTER_API_KEY: {openrouter_key[:20]}...")
    else:
        print(f"  ❌ OPENROUTER_API_KEY 未设置")
        return
    
    # 3. 初始化MCP客户端
    print("📡 初始化MCP客户端...")
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 4. 获取工具列表
    print("🔍 获取工具列表...")
    tools = mcp_client.get_available_tools()
    
    openrouter_tools = [tool for tool in tools if tool.get('server_name') == 'openrouter']
    print(f"  OpenRouter工具数量: {len(openrouter_tools)}")

    for tool in openrouter_tools:
        print(f"    - {tool['name']}: {tool['description'][:50]}...")
    
    # 5. 测试模型搜索（验证认证）
    if openrouter_tools:
        print("\n🔍 测试模型搜索...")
        try:
            result = await mcp_client.call_tool(
                'search_models',
                {'query': 'gpt'},
                'openrouter'
            )

            if result and not result.get('isError', True):
                print("  ✅ 模型搜索成功")
                content = result.get('result', [])
                if content and len(content) > 0:
                    import json
                    try:
                        models_data = json.loads(content[0].get('text', '{}'))
                        models = models_data.get('models', [])
                        print(f"  找到 {len(models)} 个模型")
                        for model in models[:3]:
                            print(f"    - {model.get('id', 'Unknown')}")
                    except json.JSONDecodeError:
                        print(f"  响应内容: {content[0].get('text', '')[:100]}...")
            else:
                print("  ❌ 模型搜索失败")
                print(f"  错误: {result}")

        except Exception as e:
            print(f"  ❌ 调用失败: {e}")
    
    print("\n🎉 认证测试完成!")

if __name__ == "__main__":
    asyncio.run(test_openrouter_auth())
