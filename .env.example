# DaVinci AI Co-pilot Pro - 环境变量配置示例
# 复制此文件为 .env 并填入实际的API密钥

# ===== LLM提供商配置 =====
# 选择LLM提供商: deepseek 或 anthropic
# 默认使用DeepSeek，因为它已经集成在插件中
LLM_PROVIDER=deepseek

# DeepSeek API配置（推荐，默认选择）
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Anthropic Claude API配置（备选）
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# ===== MCP服务器配置 =====
# MCP服务器端口
MCP_SERVER_PORT=8000

# MCP客户端超时设置（秒）
MCP_CLIENT_TIMEOUT=30

# ===== 应用配置 =====
# 应用运行端口
PORT=8080

# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 开发模式
DEBUG=false

# ===== 文件输出配置 =====
# 用户自定义输出目录（支持 ~ 表示用户主目录）
# 默认为用户桌面的 DaVinci_AI_Output 文件夹
# 示例: ~/Desktop/DaVinci_AI_Output 或 /path/to/custom/output
USER_OUTPUT_DIR=~/Desktop/DaVinci_AI_Output

# 项目内部输出目录（相对于项目根目录）
# 用于系统内部文件和缓存，通常不需要修改
PROJECT_OUTPUT_DIR=./output

# ===== MCP扩展服务配置 =====
# MiniMax API配置（AI内容生成）
MINIMAX_API_KEY=your_minimax_api_key_here
MINIMAX_GROUP_ID=your_minimax_group_id_here
MINIMAX_API_HOST=https://api.minimax.chat

# OpenRouter API配置（多模态分析）
OPENROUTER_API_KEY=your_openrouter_api_key_here

# ===== 其他服务配置 =====
# 如果使用其他外部服务，在这里添加相应的API密钥

# 示例：如果使用OpenAI服务
# OPENAI_API_KEY=your_openai_api_key_here

# 示例：如果使用Google服务
# GOOGLE_API_KEY=your_google_api_key_here

# ===== 安全配置 =====
# API访问密钥（可选，用于API安全）
# API_SECRET_KEY=your_secret_key_here

# 允许的来源（CORS配置）
# ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
