#!/usr/bin/env python3
"""
简单测试工具定义格式
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import Config<PERSON>anager
from src.services.mcp_client import MCPClient

async def test_tool_definitions():
    """测试工具定义格式"""
    print("🔧 工具定义格式测试")
    print("=" * 50)
    
    # 初始化MCP客户端
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 获取可用工具
    available_tools = mcp_client.get_available_tools()
    print(f"📊 发现 {len(available_tools)} 个可用工具")
    
    # 重点检查问题工具
    problem_tools = ['text_to_image', 'text_to_audio', 'chat_completion']
    
    for tool_name in problem_tools:
        print(f"\n🔍 检查工具: {tool_name}")
        print("-" * 40)
        
        # 查找工具
        tool_info = None
        for tool in available_tools:
            if tool.get('name') == tool_name:
                tool_info = tool
                break
        
        if not tool_info:
            print(f"   ❌ 未找到工具: {tool_name}")
            continue
        
        print(f"   ✅ 找到工具: {tool_name}")
        print(f"   服务器: {tool_info.get('server_name', 'unknown')}")
        print(f"   描述: {tool_info.get('description', 'N/A')[:100]}...")
        
        # 检查输入schema
        input_schema = tool_info.get('inputSchema', {})
        if not input_schema:
            print(f"   ❌ 缺少inputSchema")
            continue
        
        print(f"   ✅ 有inputSchema")
        
        # 检查properties
        properties = input_schema.get('properties', {})
        if not properties:
            print(f"   ❌ inputSchema缺少properties")
            continue
        
        print(f"   ✅ 有properties，包含 {len(properties)} 个字段:")
        for prop_name, prop_def in properties.items():
            prop_type = prop_def.get('type', 'unknown')
            prop_desc = prop_def.get('description', 'N/A')[:50]
            print(f"      - {prop_name} ({prop_type}): {prop_desc}...")
        
        # 检查required字段
        required = input_schema.get('required', [])
        if not required:
            print(f"   ⚠️  缺少required字段")
        else:
            print(f"   ✅ 有required字段: {required}")
        
        # 检查是否有默认值问题
        has_empty_defaults = False
        for prop_name, prop_def in properties.items():
            if 'default' in prop_def and prop_def['default'] == "":
                print(f"   ⚠️  {prop_name} 有空字符串默认值")
                has_empty_defaults = True
        
        if not has_empty_defaults:
            print(f"   ✅ 无空默认值问题")
        
        # 生成OpenAI格式的工具定义
        print(f"\n   📋 OpenAI格式工具定义:")
        openai_tool = {
            "type": "function",
            "function": {
                "name": tool_name,
                "description": tool_info.get('description', ''),
                "parameters": input_schema
            }
        }
        
        # 检查转换后的格式
        try:
            json_str = json.dumps(openai_tool, indent=2, ensure_ascii=False)
            print(f"   ✅ JSON格式正确")
            
            # 显示关键部分
            params = openai_tool['function']['parameters']
            print(f"   参数类型: {params.get('type', 'N/A')}")
            print(f"   必需参数: {params.get('required', [])}")
            print(f"   参数数量: {len(params.get('properties', {}))}")
            
        except Exception as e:
            print(f"   ❌ JSON格式错误: {e}")
    
    # 总结分析
    print(f"\n📊 **问题分析总结**")
    print("=" * 50)
    
    print("常见导致DeepSeek空参数的问题：")
    print("1. ❌ 工具定义缺少required字段")
    print("2. ❌ 参数有空字符串默认值")
    print("3. ❌ 参数描述不够清晰")
    print("4. ❌ 参数结构过于复杂")
    print("5. ❌ 系统prompt缺少具体示例")
    
    print(f"\n🔧 **修复建议**:")
    print("1. 确保所有工具都有required字段")
    print("2. 移除所有空字符串默认值")
    print("3. 简化参数结构")
    print("4. 在系统prompt中添加具体调用示例")
    
    # 检查具体的工具转换逻辑
    print(f"\n🔍 **工具转换测试**")
    print("-" * 30)
    
    # 模拟DeepSeek工具转换
    from src.services.llm_client import DeepSeekLLMClient
    
    # 创建一个临时的工具转换测试
    test_tool = {
        'name': 'text_to_image',
        'description': '生成图像',
        'inputSchema': {
            'type': 'object',
            'properties': {
                'prompt': {
                    'type': 'string',
                    'description': '图像描述'
                }
            },
            'required': ['prompt']
        }
    }
    
    # 测试工具转换
    try:
        # 模拟工具转换逻辑
        converted_tool = {
            "type": "function",
            "function": {
                "name": test_tool['name'],
                "description": test_tool['description'],
                "parameters": test_tool['inputSchema']
            }
        }
        
        print(f"✅ 工具转换成功:")
        print(f"   名称: {converted_tool['function']['name']}")
        print(f"   参数: {converted_tool['function']['parameters']}")
        
        # 检查关键字段
        params = converted_tool['function']['parameters']
        if params.get('required'):
            print(f"   ✅ 有required字段: {params['required']}")
        else:
            print(f"   ❌ 缺少required字段")
            
    except Exception as e:
        print(f"❌ 工具转换失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_tool_definitions())
