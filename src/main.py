"""
DaVinci AI Co-pilot PRO 主应用入口
FastAPI应用 + WebSocket支持 + 静态文件服务
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv(project_root / ".env")

# 修复SSL证书问题：移除可能导致问题的SSL_CERT_FILE环境变量
if "SSL_CERT_FILE" in os.environ:
    del os.environ["SSL_CERT_FILE"]
    logger.info("🔧 Removed SSL_CERT_FILE environment variable to use system default certificates")

from src.core import (
    config_manager,
    get_config,
    validate_config,
    error_handler,
    BasePluginError
)
# MCP-First架构 - 直接使用MCP客户端和LLM客户端
from src.api.routes import router as api_router
# 可选的可视化路由（如果需要工作流可视化功能）
# from src.api.visualization_routes import router as visualization_router

# 配置日志
def setup_logging():
    """配置日志系统"""
    # 移除默认的loguru处理器
    logger.remove()

    # 添加控制台输出
    logger.add(
        sys.stderr,
        level=get_config('logging.level', 'DEBUG'),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )

    # 添加文件输出
    log_file = get_config('logging.file', 'logs/app.log')
    log_dir = Path(log_file).parent
    log_dir.mkdir(exist_ok=True)

    logger.add(
        log_file,
        level=get_config('logging.level', 'INFO'),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation=get_config('logging.max_size', '10MB'),
        retention=get_config('logging.backup_count', 5),
        compression="zip"
    )

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("� Starting DaVinci AI Co-pilot PRO...")

    # 验证配置
    if not validate_config():
        logger.error("❌ Configuration validation failed")
        sys.exit(1)

    # MCP-First架构：不再需要初始化AI服务管理器
    # MCP客户端在需要时自动初始化
    logger.info("✅ MCP-First架构：跳过AI服务管理器初始化")

    # 初始化DaVinci Resolve API
    try:
        from .davinci import resolve_api
        await resolve_api.initialize()
        logger.info("✅ DaVinci Resolve API initialized")
    except Exception as e:
        logger.warning(f"⚠️ DaVinci Resolve API initialization failed: {e}")
        logger.info("💡 This is normal if DaVinci Resolve is not running")

    # 创建必要的目录
    for dir_key in ['storage.temp_dir', 'storage.output_dir', 'storage.cache_dir']:
        dir_path = Path(get_config(dir_key, './temp'))
        dir_path.mkdir(parents=True, exist_ok=True)

    logger.info("🎉 Application startup complete")

    yield

    # 关闭时执行
    logger.info("🛑 Shutting down DaVinci AI Co-pilot PRO...")

    # MCP-First架构：不需要显式关闭AI服务管理器
    # MCP客户端会自动清理资源
    logger.info("✅ MCP-First架构：跳过AI服务管理器关闭")

    # 配置管理器清理（如果需要的话）
    # config_manager.stop_watcher()  # 方法不存在，暂时注释

    logger.info("👋 Application shutdown complete")

# 创建FastAPI应用
app = FastAPI(
    title="DaVinci AI Co-pilot PRO",
    description="Professional DaVinci Resolve AI Assistant Plugin",
    version="0.1.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=get_config('security.cors_origins', ["http://localhost:3000", "http://127.0.0.1:8000"]),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router)

# 暂时注释掉依赖旧架构的路由
# 注册NLP Function Call路由
# app.include_router(nlp_router)

# 注册工作流可视化路由
# app.include_router(visualization_router)

# 注册MCP健康监控路由
# app.include_router(mcp_health_router)

# 动态配置路由已移除 - 简化架构不需要复杂的动态配置功能

# 注册DaVinci Resolve路由
try:
    from src.api.davinci_routes import router as davinci_router
    app.include_router(davinci_router)
    logger.info("DaVinci Resolve API routes registered")
except ImportError as e:
    logger.warning(f"DaVinci Resolve routes not available: {e}")

# 注册媒体库路由
try:
    from web.api.media_library import router as media_router
    app.include_router(media_router)
    logger.info("Media Library API routes registered")
except ImportError as e:
    logger.warning(f"Media Library API routes not available: {e}")

# 配置静态文件和模板
web_dir = project_root / "web"

# 🔧 使用PathManager获取用户自定义输出目录和项目输出目录
from src.core.path_manager import PathManager
path_manager = PathManager()
user_output_dir = Path(path_manager.get_output_directory())  # 用户自定义输出目录
project_output_dir = Path(path_manager.get_project_output_directory())  # 项目内部输出目录

if web_dir.exists():
    app.mount("/static", StaticFiles(directory=str(web_dir / "static")), name="static")
    templates = Jinja2Templates(directory=str(web_dir / "templates"))

    # 挂载配置文件目录
    config_dir = project_root / "config"
    if config_dir.exists():
        app.mount("/config", StaticFiles(directory=str(config_dir)), name="config")
        logger.info(f"Config directory mounted at /config -> {config_dir}")

    # 添加测试页面路由
    from fastapi.responses import FileResponse

    @app.get("/test_voice_selector.html")
    async def test_voice_selector():
        return FileResponse("test_voice_selector.html")

    @app.get("/nlp-function-call.html")
    async def nlp_function_call():
        return FileResponse(str(web_dir / "nlp-function-call.html"))

    # 挂载整个web目录作为静态文件
    app.mount("/web", StaticFiles(directory=str(web_dir)), name="web")

else:
    templates = None
    logger.warning("Web directory not found, static files and templates disabled")

# 🔧 配置用户自定义输出目录的静态文件服务，用于访问生成的文件
if user_output_dir.exists():
    app.mount("/user-output", StaticFiles(directory=str(user_output_dir)), name="user_output")
    logger.info(f"用户输出目录已挂载: /user-output -> {user_output_dir}")
else:
    # 如果用户输出目录不存在，创建它
    user_output_dir.mkdir(parents=True, exist_ok=True)
    app.mount("/user-output", StaticFiles(directory=str(user_output_dir)), name="user_output")
    logger.info(f"用户输出目录已创建并挂载: /user-output -> {user_output_dir}")

# 配置项目内部输出目录的静态文件服务
if project_output_dir.exists():
    app.mount("/output", StaticFiles(directory=str(project_output_dir)), name="project_output")
    logger.info(f"项目输出目录已挂载: /output -> {project_output_dir}")
else:
    # 如果项目输出目录不存在，创建它
    project_output_dir.mkdir(parents=True, exist_ok=True)
    app.mount("/output", StaticFiles(directory=str(project_output_dir)), name="project_output")
    logger.info(f"项目输出目录已创建并挂载: /output -> {project_output_dir}")

# WebSocket连接管理
class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: list[WebSocket] = []
        self.max_connections = get_config('websocket.max_connections', 50)  # 降低限制

    async def connect(self, websocket: WebSocket):
        # 简化连接检查
        if len(self.active_connections) >= self.max_connections:
            await websocket.close(code=1008, reason="Too many connections")
            logger.warning(f"WebSocket connection rejected: max connections ({self.max_connections}) reached")
            return False

        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
        return True

    async def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            await self.disconnect(websocket)

    async def broadcast(self, message: str):
        # 简化广播逻辑
        disconnected_connections = []

        for connection in self.active_connections.copy():
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                disconnected_connections.append(connection)

        # 清理断开的连接
        for conn in disconnected_connections:
            if conn in self.active_connections:
                self.active_connections.remove(conn)

manager = ConnectionManager()

# 路由定义
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """主页"""
    if templates:
        return templates.TemplateResponse(request, "index.html")
    else:
        return HTMLResponse("""
        <html>
            <head><title>DaVinci AI Co-pilot PRO</title></head>
            <body>
                <h1>🎬 DaVinci AI Co-pilot PRO</h1>
                <p>Professional DaVinci Resolve AI Assistant Plugin</p>
                <p>Status: <span style="color: green;">Running</span></p>
                <p>API Documentation: <a href="/docs">/docs</a></p>
            </body>
        </html>
        """)

@app.get("/demo", response_class=HTMLResponse)
async def components_demo(request: Request):
    """组件演示页面"""
    if templates:
        return templates.TemplateResponse(request, "components_demo.html")
    else:
        return HTMLResponse("""
        <html>
            <head><title>组件演示 - DaVinci AI Co-pilot PRO</title></head>
            <body>
                <h1>组件演示页面</h1>
                <p>Web templates not available. Please check the web directory.</p>
            </body>
        </html>
        """)

@app.get("/health")
async def health_check():
    """健康检查 - MCP-First架构简化版"""
    return {
        "status": "healthy",
        "version": "0.1.0",
        "architecture": "MCP-First",
        "services": {
            "deepseek": {"status": "available", "type": "direct_api"},
            "mcp_clients": {"status": "available", "type": "dynamic"}
        },
        "capabilities": ["text_generation", "translation", "text_analysis"],
        "timestamp": asyncio.get_event_loop().time()
    }

@app.get("/api/services/stats")
async def get_service_stats():
    """获取服务统计信息 - MCP-First架构简化版"""
    return {
        "deepseek": {"status": "available", "requests": 0, "type": "direct_api"},
        "mcp_clients": {"status": "available", "requests": 0, "type": "dynamic"},
        "total_requests": 0,
        "architecture": "MCP-First"
    }

@app.get("/api/services/capabilities")
async def get_service_capabilities():
    """获取服务能力 - MCP-First架构简化版"""
    return ["text_generation", "translation", "text_analysis", "speech_synthesis", "image_generation"]

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点"""
    connected = await manager.connect(websocket)
    if not connected:
        return

    try:
        while True:
            # 添加超时控制，避免长时间阻塞
            data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
            logger.info(f"Received WebSocket message: {data}")

            # 这里可以处理WebSocket消息
            # 例如：实时状态更新、进度通知等

            await manager.send_personal_message(f"Echo: {data}", websocket)

    except WebSocketDisconnect:
        await manager.disconnect(websocket)
    except asyncio.TimeoutError:
        logger.info("WebSocket timeout, closing connection")
        await manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await manager.disconnect(websocket)

# 全局异常处理
@app.exception_handler(BasePluginError)
async def plugin_exception_handler(request: Request, exc: BasePluginError):
    """插件异常处理"""
    from fastapi.responses import JSONResponse
    error_handler.handle_error(exc)
    return JSONResponse(
        status_code=400,
        content={
            "success": False,
            "error": exc.message,
            "data": None,
            "metadata": {
                "error_code": exc.error_code.value,
                "details": exc.details
            }
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理"""
    from fastapi.responses import JSONResponse
    logger.exception(f"Unhandled exception occurred: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error occurred",
            "data": None,
            "metadata": {
                "error_code": "INTERNAL_SERVER_ERROR",
                "details": {"exception_type": type(exc).__name__}
            }
        }
    )

def main():
    """主函数"""
    # 设置日志
    setup_logging()

    # 获取配置
    host = get_config('app.host', '127.0.0.1')
    port = get_config('app.port', 8000)
    debug = get_config('app.debug', False)

    logger.info(f"🎬 Starting DaVinci AI Co-pilot PRO on {host}:{port}")
    logger.info(f"🔧 Debug mode: {debug}")
    logger.info(f"📁 Project root: {project_root}")

    # 启动服务器
    uvicorn.run(
        "src.main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug",
        access_log=True
    )

if __name__ == "__main__":
    main()
