"""
MCP标准客户端
基于MCP官方协议实现标准的MCP客户端，支持多服务器连接和工具发现
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field


# MCP官方SDK依赖
# 需要安装: pip install mcp
try:
    from mcp import ClientSession, StdioServerParameters, types
    from mcp.client.stdio import stdio_client
    MCP_AVAILABLE = True
except ImportError:
    logging.warning("MCP SDK not installed. Please install with: pip install mcp")
    MCP_AVAILABLE = False
    # 提供模拟类以避免导入错误
    class types:
        class TextContent:
            def __init__(self, type, text):
                self.type = type
                self.text = text
        class ImageContent:
            def __init__(self, type, data, mimeType):
                self.type = type
                self.data = data
                self.mimeType = mimeType
        class EmbeddedResource:
            def __init__(self, type, resource):
                self.type = type
                self.resource = resource

    class ClientSession:
        async def initialize(self): pass
        async def list_tools(self): return type('obj', (object,), {'tools': []})()
        async def call_tool(self, name, args): return type('obj', (object,), {'content': []})()

    class StdioServerParameters:
        def __init__(self, command, args, env=None):
            self.command = command
            self.args = args
            self.env = env

    async def stdio_client(params):
        return None, None


@dataclass
class MCPToolInfo:
    """MCP工具信息"""
    name: str
    description: str
    input_schema: Dict[str, Any]
    server_name: str
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class MCPServerConfig:
    """MCP服务器配置"""
    name: str
    command: str
    args: List[str]
    env: Dict[str, str] = field(default_factory=dict)


@dataclass
class MCPServerSession:
    """MCP服务器会话"""
    config: MCPServerConfig
    server_params: Optional[Any] = None  # 保存服务器参数用于按需连接
    tools: Dict[str, MCPToolInfo] = field(default_factory=dict)
    connected: bool = False
    last_discovery: Optional[datetime] = None
    error_message: Optional[str] = None


class MCPClient:
    """
    MCP标准客户端
    基于MCP官方协议实现多服务器连接管理和工具发现
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化MCP客户端
        
        Args:
            config_path: MCP配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path
        
        # 服务器会话管理
        self.server_sessions: Dict[str, MCPServerSession] = {}
        
        # 工具注册表 - 工具名称到服务器的映射
        self.tool_registry: Dict[str, str] = {}  # tool_name -> server_name

        # 工具冲突处理 - 同名工具的多个提供者
        self.tool_conflicts: Dict[str, List[str]] = {}  # tool_name -> [server_names]

        # 所有可用工具
        self.available_tools: Dict[str, MCPToolInfo] = {}

        # 服务器优先级（用于冲突解决）
        self.server_priority: Dict[str, int] = {}  # server_name -> priority (lower = higher priority)
        
        self._initialized = False
    
    async def initialize(self) -> bool:
        """
        初始化MCP客户端，加载配置并连接所有服务器
        
        Returns:
            bool: 初始化是否成功
        """
        if not MCP_AVAILABLE:
            self.logger.error("MCP SDK not available. Please install with: pip install mcp")
            return False
        
        try:
            # 加载配置
            config = self._load_config()
            if not config:
                self.logger.error("Failed to load MCP configuration")
                return False
            
            # 连接所有服务器
            success_count = 0
            # 支持两种配置格式：直接的servers或mcp.servers
            servers_config = config.get('servers', {})
            if not servers_config and 'mcp' in config:
                servers_config = config['mcp'].get('servers', {})

            # 设置服务器优先级（配置文件中的顺序决定优先级）
            for i, server_name in enumerate(servers_config.keys()):
                self.server_priority[server_name] = i

            for server_name, server_config in servers_config.items():
                if await self._connect_server(server_name, server_config):
                    success_count += 1

            self.logger.info(f"Connected to {success_count}/{len(servers_config)} MCP servers")
            
            # 发现所有工具
            await self._discover_all_tools()
            
            self._initialized = True
            return success_count > 0

        except (ConnectionError, TimeoutError) as e:
            self.logger.exception("Failed to initialize MCP client due to connection issues")
            return False
        except Exception as e:
            self.logger.exception("Unexpected error during MCP client initialization")
            return False
    
    def _load_config(self) -> Optional[Dict[str, Any]]:
        """
        加载MCP配置文件
        
        Returns:
            Dict: 配置字典，失败返回None
        """
        if not self.config_path:
            # 默认配置路径 - 从项目根目录启动
            self.config_path = "config/unified_config_simplified.json"
        
        try:
            if not os.path.exists(self.config_path):
                self.logger.error(f"MCP config file not found: {self.config_path}")
                return None

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 处理环境变量替换
            config = self._resolve_env_variables(config)

            return config

        except (OSError, PermissionError) as e:
            self.logger.exception(f"Failed to read config file {self.config_path}")
            return None
        except json.JSONDecodeError as e:
            self.logger.exception(f"Invalid JSON in config file {self.config_path}")
            return None
        except Exception as e:
            self.logger.exception(f"Unexpected error loading config from {self.config_path}")
            return None
    
    def _resolve_env_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析配置中的环境变量
        
        Args:
            config: 原始配置
            
        Returns:
            Dict: 解析后的配置
        """
        def resolve_value(value):
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_var = value[2:-1]
                return os.getenv(env_var, value)
            elif isinstance(value, dict):
                return {k: resolve_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [resolve_value(item) for item in value]
            return value
        
        return resolve_value(config)
    
    async def _connect_server(self, server_name: str, server_config: Dict[str, Any]) -> bool:
        """
        连接单个MCP服务器
        
        Args:
            server_name: 服务器名称
            server_config: 服务器配置
            
        Returns:
            bool: 连接是否成功
        """
        try:
            # 创建服务器配置对象
            mcp_config = MCPServerConfig(
                name=server_name,
                command=server_config.get('command', ''),
                args=server_config.get('args', []),
                env=server_config.get('env', {})
            )
            
            # 创建服务器会话
            session_info = MCPServerSession(config=mcp_config)
            
            # 设置服务器参数
            server_params = StdioServerParameters(
                command=mcp_config.command,
                args=mcp_config.args,
                env=mcp_config.env if mcp_config.env else None
            )
            
            # 保存服务器配置信息（按需建立连接，不进行预连接测试）
            session_info.connected = True  # 假设配置正确就能连接
            session_info.server_params = server_params  # 保存参数用于按需连接

            self.server_sessions[server_name] = session_info
            self.logger.info(f"Configured MCP server: {server_name} (connection will be tested on first use)")
            
            self.logger.info(f"Successfully connected to MCP server: {server_name}")
            return True
            
        except (ConnectionError, TimeoutError) as e:
            self.logger.exception(f"Connection error with MCP server {server_name}")
            if server_name in self.server_sessions:
                self.server_sessions[server_name].error_message = str(e)
            return False
        except Exception as e:
            self.logger.exception(f"Unexpected error connecting to MCP server {server_name}")
            if server_name in self.server_sessions:
                self.server_sessions[server_name].error_message = str(e)
            return False
    
    async def _discover_all_tools(self) -> None:
        """
        发现所有连接服务器的工具
        """
        self.logger.info("Discovering tools from all connected MCP servers...")
        
        total_tools = 0
        for server_name, session_info in self.server_sessions.items():
            if not session_info.connected or not session_info.server_params:
                continue

            try:
                # 按需建立连接并发现工具 - 使用正确的MCP客户端模式
                self.logger.info(f"Establishing connection to {server_name}...")
                async with stdio_client(session_info.server_params) as (read, write):
                    self.logger.info(f"stdio_client connected for {server_name}")

                    # 正确使用ClientSession作为async context manager
                    async with ClientSession(read, write) as session:
                        self.logger.info(f"Initializing MCP session for {server_name}...")
                        await session.initialize()
                        self.logger.info(f"MCP session initialized for {server_name}")

                        # 调用MCP协议的list_tools()
                        self.logger.info(f"Listing tools for {server_name}...")
                        response = await session.list_tools()
                        tools = response.tools
                
                self.logger.info(f"Server {server_name} provides {len(tools)} tools: {[tool.name for tool in tools]}")
                
                # 注册工具
                for tool in tools:
                    tool_info = MCPToolInfo(
                        name=tool.name,
                        description=tool.description,
                        input_schema=tool.inputSchema,
                        server_name=server_name,
                        last_updated=datetime.now()
                    )
                    
                    # 添加到服务器工具列表
                    session_info.tools[tool.name] = tool_info

                    # 处理工具名称冲突
                    if tool.name in self.tool_registry:
                        existing_server = self.tool_registry[tool.name]
                        self.logger.warning(f"Tool name conflict: {tool.name} exists in both {existing_server} and {server_name}")

                        # 记录冲突
                        if tool.name not in self.tool_conflicts:
                            self.tool_conflicts[tool.name] = [existing_server]
                        self.tool_conflicts[tool.name].append(server_name)

                        # 根据优先级选择服务器
                        current_priority = self.server_priority.get(existing_server, 999)
                        new_priority = self.server_priority.get(server_name, 999)

                        if new_priority < current_priority:
                            self.logger.info(f"Using {server_name} for tool {tool.name} (higher priority)")
                            self.tool_registry[tool.name] = server_name
                            self.available_tools[tool.name] = tool_info
                        else:
                            self.logger.info(f"Keeping {existing_server} for tool {tool.name} (higher priority)")
                    else:
                        # 没有冲突，直接注册
                        self.tool_registry[tool.name] = server_name
                        self.available_tools[tool.name] = tool_info
                    
                    total_tools += 1
                
                session_info.last_discovery = datetime.now()
                
            except (ConnectionError, TimeoutError) as e:
                self.logger.exception(f"Connection error discovering tools from server {server_name}")
                session_info.error_message = str(e)
            except Exception as e:
                self.logger.exception(f"Unexpected error discovering tools from server {server_name}")
                session_info.error_message = str(e)
        
        self.logger.info(f"Total discovered tools: {total_tools}")
        self.logger.info(f"Available tools: {list(self.available_tools.keys())}")

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any], enable_fallback: bool = True) -> Dict[str, Any]:
        """
        调用MCP工具，支持故障转移

        Args:
            tool_name: 工具名称
            arguments: 工具参数
            enable_fallback: 是否启用故障转移

        Returns:
            Dict: 工具调用结果
        """
        if not self._initialized:
            raise RuntimeError("MCP client not initialized. Call initialize() first.")

        if tool_name not in self.tool_registry:
            raise ValueError(f"Tool '{tool_name}' not found. Available tools: {list(self.available_tools.keys())}")

        # 获取主要服务器
        primary_server = self.tool_registry[tool_name]

        # 尝试调用主要服务器
        result = await self._call_tool_on_server(tool_name, arguments, primary_server)

        # 如果成功或不启用故障转移，直接返回
        if result['success'] or not enable_fallback:
            return result

        # 故障转移：尝试其他提供相同工具的服务器
        if tool_name in self.tool_conflicts:
            self.logger.warning(f"Primary server {primary_server} failed for tool {tool_name}, trying fallback servers...")

            for fallback_server in self.tool_conflicts[tool_name]:
                if fallback_server == primary_server:
                    continue  # 跳过已经失败的主要服务器

                self.logger.info(f"Trying fallback server {fallback_server} for tool {tool_name}")
                fallback_result = await self._call_tool_on_server(tool_name, arguments, fallback_server)

                if fallback_result['success']:
                    self.logger.info(f"Fallback successful using server {fallback_server}")
                    return fallback_result

        # 所有服务器都失败
        self.logger.error(f"All servers failed for tool {tool_name}")
        return result  # 返回原始错误

    async def call_tool_stream(self, tool_name: str, arguments: Dict[str, Any], enable_fallback: bool = True):
        """
        流式调用MCP工具，支持实时状态更新

        Args:
            tool_name: 工具名称
            arguments: 工具参数
            enable_fallback: 是否启用故障转移

        Yields:
            流式工具调用状态和结果
        """
        if not self._initialized:
            yield {
                "type": "error",
                "message": "MCP client not initialized",
                "error": "MCP client not initialized. Call initialize() first."
            }
            return

        if tool_name not in self.tool_registry:
            yield {
                "type": "error",
                "message": f"Tool '{tool_name}' not found",
                "error": f"Tool '{tool_name}' not found. Available tools: {list(self.available_tools.keys())}"
            }
            return

        # 获取主要服务器
        primary_server = self.tool_registry[tool_name]

        yield {
            "type": "tool_call_start",
            "tool_name": tool_name,
            "server_name": primary_server,
            "arguments": arguments,
            "message": f"开始调用工具 {tool_name} (服务器: {primary_server})"
        }

        # 尝试调用主要服务器
        async for chunk in self._call_tool_on_server_stream(tool_name, arguments, primary_server):
            yield chunk

            # 如果成功完成，直接返回
            if chunk.get("type") == "tool_call_complete" and chunk.get("success"):
                return

            # 如果失败且不启用故障转移，返回错误
            if chunk.get("type") == "tool_call_error" and not enable_fallback:
                return

        # 故障转移：尝试其他提供相同工具的服务器
        if tool_name in self.tool_conflicts:
            yield {
                "type": "fallback_start",
                "message": f"主服务器 {primary_server} 失败，尝试故障转移...",
                "fallback_servers": self.tool_conflicts[tool_name]
            }

            for fallback_server in self.tool_conflicts[tool_name]:
                if fallback_server == primary_server:
                    continue  # 跳过已经失败的主要服务器

                yield {
                    "type": "fallback_attempt",
                    "server_name": fallback_server,
                    "message": f"尝试故障转移服务器: {fallback_server}"
                }

                success = False
                async for chunk in self._call_tool_on_server_stream(tool_name, arguments, fallback_server):
                    yield chunk
                    if chunk.get("type") == "tool_call_complete" and chunk.get("success"):
                        success = True
                        yield {
                            "type": "fallback_success",
                            "server_name": fallback_server,
                            "message": f"故障转移成功，使用服务器: {fallback_server}"
                        }
                        return

                if not success:
                    yield {
                        "type": "fallback_failed",
                        "server_name": fallback_server,
                        "message": f"故障转移服务器 {fallback_server} 也失败了"
                    }

        # 所有服务器都失败
        yield {
            "type": "all_servers_failed",
            "message": f"所有服务器都无法执行工具 {tool_name}",
            "error": "All servers failed"
        }

    async def _call_tool_on_server_stream(self, tool_name: str, arguments: Dict[str, Any], server_name: str):
        """
        在指定服务器上流式调用工具

        Args:
            tool_name: 工具名称
            arguments: 工具参数
            server_name: 服务器名称

        Yields:
            流式工具调用状态和结果
        """
        session_info = self.server_sessions.get(server_name)

        if not session_info or not session_info.connected or not session_info.server_params:
            yield {
                "type": "tool_call_error",
                "tool_name": tool_name,
                "server_name": server_name,
                "success": False,
                "error": f"Server '{server_name}' is not available",
                "message": f"服务器 {server_name} 不可用"
            }
            return

        try:
            # 预处理参数
            yield {
                "type": "tool_preprocessing",
                "message": "正在预处理工具参数...",
                "tool_name": tool_name,
                "server_name": server_name
            }

            processed_arguments = self._preprocess_tool_arguments(tool_name, arguments, server_name)

            yield {
                "type": "tool_connecting",
                "message": f"正在连接到服务器 {server_name}...",
                "tool_name": tool_name,
                "server_name": server_name
            }

            # 按需建立连接并调用工具 - 修复异步上下文管理器问题
            result = None
            processed_content = None

            try:
                async with stdio_client(session_info.server_params) as (read, write):
                    yield {
                        "type": "tool_session_start",
                        "message": f"已连接到服务器 {server_name}，正在初始化会话...",
                        "tool_name": tool_name,
                        "server_name": server_name
                    }

                    # 正确使用ClientSession作为async context manager
                    async with ClientSession(read, write) as session:
                        yield {
                            "type": "tool_session_ready",
                            "message": f"会话已初始化，正在调用工具 {tool_name}...",
                            "tool_name": tool_name,
                            "server_name": server_name
                        }

                        await session.initialize()

                        yield {
                            "type": "tool_executing",
                            "message": f"正在执行工具 {tool_name}...",
                            "tool_name": tool_name,
                            "server_name": server_name
                        }

                        # 调用MCP协议的call_tool()
                        result = await session.call_tool(tool_name, processed_arguments)

                # 在上下文管理器外部处理结果，避免异步任务管理错误
                yield {
                    "type": "tool_processing_result",
                    "message": "正在处理工具执行结果...",
                    "tool_name": tool_name,
                    "server_name": server_name
                }

                # 根据官方文档正确处理不同内容类型
                processed_content = self._process_tool_result_content(result.content)

                yield {
                    "type": "tool_call_complete",
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "success": True,
                    "result": processed_content,
                    "message": f"工具 {tool_name} 执行成功"
                }

            except Exception as context_error:
                self.logger.exception(f"MCP上下文管理器错误 - 工具: {tool_name}, 服务器: {server_name}, 错误: {context_error}")
                yield {
                    "type": "tool_call_error",
                    "tool_name": tool_name,
                    "server_name": server_name,
                    "success": False,
                    "error": f"MCP连接错误: {str(context_error)}",
                    "message": f"工具 {tool_name} 连接失败"
                }
                return

        except Exception as e:
            self.logger.exception(f"Error calling tool {tool_name} on server {server_name}: {e}")
            yield {
                "type": "tool_call_error",
                "tool_name": tool_name,
                "server_name": server_name,
                "success": False,
                "error": str(e),
                "message": f"工具 {tool_name} 执行失败: {str(e)}"
            }

    async def _call_tool_on_server(self, tool_name: str, arguments: Dict[str, Any], server_name: str) -> Dict[str, Any]:
        """
        在指定服务器上调用工具（按需建立连接）

        Args:
            tool_name: 工具名称
            arguments: 工具参数
            server_name: 服务器名称

        Returns:
            Dict: 工具调用结果
        """
        session_info = self.server_sessions.get(server_name)

        if not session_info or not session_info.connected or not session_info.server_params:
            return {
                'success': False,
                'error': f"Server '{server_name}' is not available",
                'tool_name': tool_name,
                'server_name': server_name
            }

        try:
            # 预处理参数，为必需的参数提供默认值
            processed_arguments = self._preprocess_tool_arguments(tool_name, arguments, server_name)
            self.logger.debug(f"Calling tool {tool_name} on server {server_name} with processed args: {processed_arguments}")

            # 按需建立连接并调用工具 - 使用正确的MCP客户端模式
            self.logger.info(f"Establishing connection to {server_name} for tool call...")
            async with stdio_client(session_info.server_params) as (read, write):
                self.logger.info(f"stdio_client connected for {server_name}")

                # 正确使用ClientSession作为async context manager
                async with ClientSession(read, write) as session:
                    self.logger.info(f"Initializing MCP session for {server_name}...")
                    await session.initialize()
                    self.logger.info(f"MCP session initialized for {server_name}")

                    # 调用MCP协议的call_tool()
                    self.logger.info(f"Calling tool {tool_name} on {server_name}...")
                    result = await session.call_tool(tool_name, processed_arguments)

                self.logger.debug(f"Tool {tool_name} returned: {result}")

                # 根据官方文档正确处理不同内容类型
                processed_content = self._process_tool_result_content(result.content)

                return {
                    'success': True,
                    'result': processed_content,
                    'tool_name': tool_name,
                    'server_name': server_name
                }

        except (ConnectionError, TimeoutError) as e:
            self.logger.exception(f"Connection error calling tool {tool_name} on server {server_name}")
            return {
                'success': False,
                'error': f"Connection error: {str(e)}",
                'tool_name': tool_name,
                'server_name': server_name
            }
        except Exception as e:
            self.logger.exception(f"Unexpected error calling tool {tool_name} on server {server_name}")
            return {
                'success': False,
                'error': str(e),
                'tool_name': tool_name,
                'server_name': server_name
            }

    def _preprocess_tool_arguments(self, tool_name: str, arguments: Dict[str, Any], server_name: str) -> Dict[str, Any]:
        """
        验证工具参数的完整性，不再提供硬编码默认值

        Args:
            tool_name: 工具名称
            arguments: 原始参数
            server_name: 服务器名称

        Returns:
            验证后的参数

        Raises:
            ValueError: 当参数为空或无效时
        """
        if not arguments:
            # 获取工具信息以提供更详细的错误
            tool_info = self.get_tool_info(tool_name)
            if tool_info and tool_info.input_schema:
                required_params = tool_info.input_schema.get("required", [])
                properties = tool_info.input_schema.get("properties", {})

                error_msg = f"工具 '{tool_name}' 缺少必需参数。"
                if required_params:
                    error_msg += f" 必需参数: {required_params}"
                if properties:
                    param_info = []
                    for param, schema in properties.items():
                        param_type = schema.get("type", "unknown")
                        param_desc = schema.get("description", "")
                        param_info.append(f"  - {param} ({param_type}): {param_desc}")
                    if param_info:
                        error_msg += f"\n可用参数:\n" + "\n".join(param_info)
            else:
                error_msg = f"工具 '{tool_name}' 参数为空，且无法获取工具定义信息"

            self.logger.error(error_msg)
            raise ValueError(error_msg)

        processed_args = arguments.copy()

        # 仅为minimax服务器的多媒体工具提供必要的output_directory（这是技术要求，不是业务逻辑）
        if server_name == "minimax" and tool_name in ["text_to_image", "text_to_audio", "generate_video", "music_generation", "voice_design"]:
            if "output_directory" not in processed_args or processed_args.get("output_directory") is None:
                # 🔧 使用PathManager获取用户自定义输出目录，移除硬编码
                from ..core.path_manager import PathManager
                path_manager = PathManager()
                default_output_dir = path_manager.get_output_directory()
                processed_args["output_directory"] = default_output_dir
                self.logger.info(f"为工具 {tool_name} 设置用户自定义输出目录: {default_output_dir}")

        self.logger.debug(f"验证后的工具参数 - 工具: {tool_name}, 参数: {processed_args}")
        return processed_args

    def _process_tool_result_content(self, content_list: List[Any]) -> List[Dict[str, Any]]:
        """
        根据官方文档处理工具调用结果的不同内容类型

        Args:
            content_list: 工具返回的内容列表

        Returns:
            List[Dict]: 处理后的内容列表
        """
        processed_content = []

        for content in content_list:
            if hasattr(content, 'type'):
                if content.type == 'text' and hasattr(content, 'text'):
                    # TextContent
                    processed_content.append({
                        'type': 'text',
                        'text': content.text
                    })
                elif content.type == 'image' and hasattr(content, 'data') and hasattr(content, 'mimeType'):
                    # ImageContent
                    processed_content.append({
                        'type': 'image',
                        'data': content.data,
                        'mimeType': content.mimeType
                    })
                elif content.type == 'resource' and hasattr(content, 'resource'):
                    # EmbeddedResource
                    processed_content.append({
                        'type': 'resource',
                        'resource': content.resource
                    })
                else:
                    # 未知类型，保持原样
                    processed_content.append({
                        'type': getattr(content, 'type', 'unknown'),
                        'raw_content': str(content)
                    })
            else:
                # 没有type属性，可能是字符串或其他简单类型
                if isinstance(content, str):
                    processed_content.append({
                        'type': 'text',
                        'text': content
                    })
                else:
                    processed_content.append({
                        'type': 'unknown',
                        'raw_content': str(content)
                    })

        return processed_content

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """
        获取所有可用工具的列表，格式化为LLM可用的格式

        Returns:
            List: 工具列表，每个工具包含name、description、input_schema
        """
        if not self._initialized:
            return []

        tools = []
        for tool_name, tool_info in self.available_tools.items():
            tools.append({
                'name': tool_info.name,
                'description': tool_info.description or "No description available",
                'input_schema': tool_info.input_schema,
                'server_name': tool_info.server_name
            })

        return tools

    def get_tool_info(self, tool_name: str) -> Optional[MCPToolInfo]:
        """
        获取特定工具的详细信息

        Args:
            tool_name: 工具名称

        Returns:
            MCPToolInfo: 工具信息，不存在返回None
        """
        return self.available_tools.get(tool_name)

    def get_server_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有服务器的状态信息

        Returns:
            Dict: 服务器状态字典
        """
        status = {}
        for server_name, session_info in self.server_sessions.items():
            status[server_name] = {
                'connected': session_info.connected,
                'tools_count': len(session_info.tools),
                'tools': list(session_info.tools.keys()),
                'priority': self.server_priority.get(server_name, 999),
                'last_discovery': session_info.last_discovery.isoformat() if session_info.last_discovery else None,
                'error_message': session_info.error_message
            }
        return status

    def get_tool_conflicts(self) -> Dict[str, Dict[str, Any]]:
        """
        获取工具冲突信息

        Returns:
            Dict: 工具冲突字典
        """
        conflicts = {}
        for tool_name, servers in self.tool_conflicts.items():
            primary_server = self.tool_registry.get(tool_name)
            conflicts[tool_name] = {
                'primary_server': primary_server,
                'alternative_servers': [s for s in servers if s != primary_server],
                'total_providers': len(servers) + (1 if primary_server and primary_server not in servers else 0)
            }
        return conflicts

    def set_tool_primary_server(self, tool_name: str, server_name: str) -> bool:
        """
        手动设置工具的主要服务器

        Args:
            tool_name: 工具名称
            server_name: 服务器名称

        Returns:
            bool: 设置是否成功
        """
        if tool_name not in self.available_tools:
            self.logger.error(f"Tool {tool_name} not found")
            return False

        # 检查服务器是否提供该工具
        session_info = self.server_sessions.get(server_name)
        if not session_info or tool_name not in session_info.tools:
            self.logger.error(f"Server {server_name} does not provide tool {tool_name}")
            return False

        # 更新主要服务器
        old_server = self.tool_registry.get(tool_name)
        self.tool_registry[tool_name] = server_name
        self.available_tools[tool_name] = session_info.tools[tool_name]

        self.logger.info(f"Changed primary server for tool {tool_name} from {old_server} to {server_name}")
        return True

    async def refresh_tools(self, server_name: Optional[str] = None) -> bool:
        """
        刷新工具发现

        Args:
            server_name: 指定服务器名称，None表示刷新所有服务器

        Returns:
            bool: 刷新是否成功
        """
        if not self._initialized:
            return False

        try:
            if server_name:
                # 刷新指定服务器
                if server_name not in self.server_sessions:
                    self.logger.error(f"Server {server_name} not found")
                    return False

                session_info = self.server_sessions[server_name]
                if not session_info.connected or not session_info.session:
                    self.logger.error(f"Server {server_name} is not connected")
                    return False

                # 清除旧工具
                for tool_name in list(session_info.tools.keys()):
                    if tool_name in self.tool_registry and self.tool_registry[tool_name] == server_name:
                        del self.tool_registry[tool_name]
                        del self.available_tools[tool_name]
                session_info.tools.clear()

                # 重新发现工具
                await self._discover_server_tools(server_name, session_info)

            else:
                # 刷新所有服务器
                self.tool_registry.clear()
                self.available_tools.clear()
                for session_info in self.server_sessions.values():
                    session_info.tools.clear()

                await self._discover_all_tools()

            return True

        except (ConnectionError, TimeoutError) as e:
            self.logger.exception("Connection error during tools refresh")
            return False
        except Exception as e:
            self.logger.exception("Unexpected error during tools refresh")
            return False

    async def _discover_server_tools(self, server_name: str, session_info: MCPServerSession) -> None:
        """
        发现单个服务器的工具

        Args:
            server_name: 服务器名称
            session_info: 服务器会话信息
        """
        try:
            # 按需建立连接并发现工具 - 使用正确的MCP客户端模式
            self.logger.info(f"Establishing connection to {server_name} for tool discovery...")
            async with stdio_client(session_info.server_params) as (read, write):
                self.logger.info(f"stdio_client connected for {server_name}")

                # 正确使用ClientSession作为async context manager
                async with ClientSession(read, write) as session:
                    self.logger.info(f"Initializing MCP session for {server_name}...")
                    await session.initialize()
                    self.logger.info(f"MCP session initialized for {server_name}")

                    self.logger.info(f"Listing tools for {server_name}...")
                    response = await session.list_tools()
                    tools = response.tools

            self.logger.info(f"Server {server_name} provides {len(tools)} tools: {[tool.name for tool in tools]}")

            for tool in tools:
                tool_info = MCPToolInfo(
                    name=tool.name,
                    description=tool.description,
                    input_schema=tool.inputSchema,
                    server_name=server_name,
                    last_updated=datetime.now()
                )

                session_info.tools[tool.name] = tool_info

                if tool.name in self.tool_registry:
                    self.logger.warning(f"Tool name conflict: {tool.name} exists in both {self.tool_registry[tool.name]} and {server_name}")

                self.tool_registry[tool.name] = server_name
                self.available_tools[tool.name] = tool_info

            session_info.last_discovery = datetime.now()

        except (ConnectionError, TimeoutError) as e:
            self.logger.exception(f"Connection error discovering tools from server {server_name}")
            session_info.error_message = str(e)
        except Exception as e:
            self.logger.exception(f"Unexpected error discovering tools from server {server_name}")
            session_info.error_message = str(e)

    async def cleanup(self) -> None:
        """
        清理资源（按需连接模式下无需关闭持久连接）
        """
        if not self._initialized:
            return

        try:
            # 清理服务器会话信息
            self.server_sessions.clear()

            # 清理工具信息
            self.available_tools.clear()
            self.tool_registry.clear()
            self.tool_conflicts.clear()

            self._initialized = False
            self.logger.info("MCP client cleanup completed")

        except Exception as e:
            self.logger.exception("Error during MCP client cleanup")

    def __del__(self):
        """析构函数"""
        # 在析构函数中不执行异步清理，避免问题
        pass
