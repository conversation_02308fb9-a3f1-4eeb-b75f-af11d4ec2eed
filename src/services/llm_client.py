"""
LLM客户端模块 - 实现真正的MCP-First架构
基于MCP官方文档的标准工作流程实现LLM与MCP工具的集成
支持多种LLM提供商：Anthropic Claude、DeepSeek等
"""

import asyncio
import json
import logging
import os
import time
from typing import Any, Dict, List, Optional, Union
from abc import ABC, abstractmethod
from datetime import datetime

import anthropic
from anthropic.types import Message, TextBlock, ToolUseBlock

from .mcp_client import MCPClient
from .multi_stage_prompt_injector import MultiStagePromptInjector, PromptStage

logger = logging.getLogger(__name__)


class ProcessingTracker:
    """处理过程跟踪器 - 支持流式状态管理"""

    def __init__(self):
        self.steps = []
        self.tool_calls = []
        self.start_time = time.time()
        self.current_step_index = None
        self.stream_callbacks = []  # 流式回调函数列表
        self.global_progress = 0.0
        self.status_message = "初始化中..."

    def add_step(self, name: str, description: str):
        """添加处理步骤"""
        step = {
            "name": name,
            "description": description,
            "status": "processing",
            "progress": 0.0,
            "duration_ms": None,
            "error": None,
            "tool_calls": [],
            "start_time": time.time()
        }
        self.steps.append(step)
        return len(self.steps) - 1

    def complete_step(self, step_index: int, progress: float = 100.0):
        """完成步骤"""
        if step_index < len(self.steps):
            step = self.steps[step_index]
            step["status"] = "completed"
            step["progress"] = progress
            step["duration_ms"] = (time.time() - step["start_time"]) * 1000

    def update_step_progress(self, step_index: int, progress: float, description: str = None):
        """更新步骤进度"""
        if step_index < len(self.steps):
            step = self.steps[step_index]
            step["progress"] = progress
            if description:
                step["description"] = description

    def fail_step(self, step_index: int, error: str):
        """步骤失败"""
        if step_index < len(self.steps):
            step = self.steps[step_index]
            step["status"] = "error"
            step["error"] = error
            step["duration_ms"] = (time.time() - step["start_time"]) * 1000

    def add_tool_call(self, name: str, server: str, arguments: dict, result: Any = None,
                     success: bool = True, error: str = None, duration_ms: float = None):
        """添加工具调用记录"""
        tool_call = {
            "name": name,
            "server": server,
            "arguments": arguments,
            "result": result,
            "success": success,
            "error": error,
            "duration_ms": duration_ms,
            "timestamp": datetime.now().isoformat()
        }
        self.tool_calls.append(tool_call)

        # 将工具调用添加到当前步骤
        if self.steps:
            current_step = self.steps[-1]
            if current_step["status"] == "processing":
                current_step["tool_calls"].append(tool_call)

    def get_total_duration(self):
        """获取总处理时间"""
        return (time.time() - self.start_time) * 1000

    def add_stream_callback(self, callback):
        """添加流式回调函数"""
        self.stream_callbacks.append(callback)

    def remove_stream_callback(self, callback):
        """移除流式回调函数"""
        if callback in self.stream_callbacks:
            self.stream_callbacks.remove(callback)

    async def _notify_stream_callbacks(self, data: dict):
        """通知所有流式回调函数"""
        for callback in self.stream_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                logger.error(f"流式回调函数执行失败: {e}")

    async def update_global_status(self, message: str, progress: float = None):
        """更新全局状态"""
        self.status_message = message
        if progress is not None:
            self.global_progress = progress

        # 通知流式回调
        await self._notify_stream_callbacks({
            "type": "global_status",
            "message": message,
            "progress": self.global_progress,
            "timestamp": datetime.now().isoformat()
        })

    async def start_step_stream(self, name: str, description: str):
        """开始步骤（流式版本）"""
        step_index = self.add_step(name, description)
        self.current_step_index = step_index

        # 通知流式回调
        await self._notify_stream_callbacks({
            "type": "step_start",
            "step_index": step_index,
            "name": name,
            "description": description,
            "timestamp": datetime.now().isoformat()
        })

        return step_index

    async def update_step_progress_stream(self, step_index: int, progress: float, description: str = None):
        """更新步骤进度（流式版本）"""
        self.update_step_progress(step_index, progress, description)

        # 通知流式回调
        await self._notify_stream_callbacks({
            "type": "step_progress",
            "step_index": step_index,
            "progress": progress,
            "description": description,
            "timestamp": datetime.now().isoformat()
        })

    async def complete_step_stream(self, step_index: int, progress: float = 100.0):
        """完成步骤（流式版本）"""
        self.complete_step(step_index, progress)

        # 通知流式回调
        await self._notify_stream_callbacks({
            "type": "step_complete",
            "step_index": step_index,
            "progress": progress,
            "duration_ms": self.steps[step_index]["duration_ms"] if step_index < len(self.steps) else None,
            "timestamp": datetime.now().isoformat()
        })

    async def fail_step_stream(self, step_index: int, error: str):
        """步骤失败（流式版本）"""
        self.fail_step(step_index, error)

        # 通知流式回调
        await self._notify_stream_callbacks({
            "type": "step_error",
            "step_index": step_index,
            "error": error,
            "timestamp": datetime.now().isoformat()
        })

    async def add_tool_call_stream(self, name: str, server: str, arguments: dict, result: Any = None,
                                 success: bool = True, error: str = None, duration_ms: float = None):
        """添加工具调用记录（流式版本）"""
        self.add_tool_call(name, server, arguments, result, success, error, duration_ms)

        # 通知流式回调
        await self._notify_stream_callbacks({
            "type": "tool_call_complete",
            "name": name,
            "server": server,
            "arguments": arguments,
            "result": result,
            "success": success,
            "error": error,
            "duration_ms": duration_ms,
            "timestamp": datetime.now().isoformat()
        })

    def get_current_status(self):
        """获取当前状态摘要"""
        return {
            "global_progress": self.global_progress,
            "status_message": self.status_message,
            "current_step_index": self.current_step_index,
            "total_steps": len(self.steps),
            "total_tool_calls": len(self.tool_calls),
            "total_duration_ms": self.get_total_duration(),
            "timestamp": datetime.now().isoformat()
        }


class BaseLLMClient(ABC):
    """LLM客户端基类 - 定义标准接口"""

    def __init__(self, mcp_client: MCPClient):
        """
        初始化LLM客户端基类

        Args:
            mcp_client: MCP客户端实例
        """
        self.mcp_client = mcp_client

    @abstractmethod
    async def process_request(self, user_query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理用户请求 - MCP协议标准工作流程

        Args:
            user_query: 用户的自然语言请求
            context: 可选的上下文信息

        Returns:
            处理结果字典
        """
        pass

    async def process_request_stream(self, user_query: str, context: Optional[Dict[str, Any]] = None):
        """
        流式处理用户请求 - 可选实现

        Args:
            user_query: 用户的自然语言请求
            context: 可选的上下文信息

        Yields:
            流式处理数据块
        """
        # 默认实现：降级到非流式处理
        result = await self.process_request(user_query, context)
        yield {
            "type": "complete",
            "result": result,
            "progress": 1.0
        }


class AnthropicLLMClient(BaseLLMClient):
    """Anthropic Claude LLM客户端 - 实现MCP协议标准的LLM工具选择机制"""

    def __init__(self, anthropic_api_key: str, mcp_client: MCPClient):
        """
        初始化Anthropic LLM客户端

        Args:
            anthropic_api_key: Anthropic API密钥
            mcp_client: MCP客户端实例
        """
        super().__init__(mcp_client)
        self.anthropic = anthropic.Anthropic(
            api_key=anthropic_api_key,
            timeout=90.0,  # 设置90秒超时
            max_retries=2  # 设置重试次数
        )
        self.model = "claude-3-5-sonnet-20241022"

    async def process_request(self, user_query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理用户请求 - MCP协议标准工作流程

        Args:
            user_query: 用户的自然语言请求
            context: 可选的上下文信息

        Returns:
            处理结果字典
        """
        try:
            logger.info(f"[ANTHROPIC] Processing user request: {user_query[:100]}...")

            # 1. 获取所有可用工具 (MCP标准步骤1)
            available_tools = await self._get_available_tools()
            if not available_tools:
                return {
                    "success": False,
                    "error": "No MCP tools available",
                    "response": "抱歉，当前没有可用的工具来处理您的请求。"
                }

            # 2. 构建消息历史
            messages = self._build_messages(user_query, context)

            # 3. 让LLM选择和调用工具 (MCP标准步骤2-3)
            response = await self._process_with_llm(messages, available_tools)

            return {
                "success": True,
                "response": response,
                "tools_used": self._extract_tools_used(response)
            }

        except Exception as e:
            logger.exception(f"[ANTHROPIC] Error processing request: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": f"处理请求时发生错误：{str(e)}"
            }
    
    async def _get_available_tools(self) -> List[Dict[str, Any]]:
        """
        获取所有可用的MCP工具
        
        Returns:
            格式化后的工具列表，符合Anthropic API要求
        """
        try:
            # 使用MCP标准方法获取工具 (同步方法，不需要await)
            tools_data = self.mcp_client.get_available_tools()

            # 转换为Anthropic API格式
            formatted_tools = []
            for tool_info in tools_data:
                tool_name = tool_info.get("name")
                formatted_tool = {
                    "name": tool_name,
                    "description": tool_info.get("description", f"Tool: {tool_name}"),
                    "input_schema": tool_info.get("input_schema", {
                        "type": "object",
                        "properties": {},
                        "required": []
                    })
                }
                formatted_tools.append(formatted_tool)
            
            logger.info(f"Found {len(formatted_tools)} available tools")
            return formatted_tools
            
        except Exception as e:
            logger.exception(f"Error getting available tools: {e}")
            return []
    
    def _build_messages(self, user_query: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """
        构建消息历史
        
        Args:
            user_query: 用户查询
            context: 上下文信息
            
        Returns:
            消息列表
        """
        messages = []
        
        # 添加系统上下文（如果有）
        if context:
            system_message = self._build_system_message(context)
            if system_message:
                messages.append({"role": "system", "content": system_message})
        
        # 添加用户查询
        messages.append({"role": "user", "content": user_query})
        
        return messages
    
    def _build_system_message(self, context: Dict[str, Any]) -> str:
        """
        构建系统消息
        
        Args:
            context: 上下文信息
            
        Returns:
            系统消息内容
        """
        system_parts = []
        
        if context.get("user_preferences"):
            system_parts.append(f"用户偏好：{context['user_preferences']}")
        
        if context.get("previous_context"):
            system_parts.append(f"之前的对话上下文：{context['previous_context']}")
        
        if system_parts:
            return "请根据以下上下文信息处理用户请求：\n" + "\n".join(system_parts)
        
        return ""
    
    async def _process_with_llm(self, messages: List[Dict[str, str]], available_tools: List[Dict[str, Any]]) -> str:
        """
        使用LLM处理请求并执行工具调用
        
        Args:
            messages: 消息历史
            available_tools: 可用工具列表
            
        Returns:
            最终响应内容
        """
        try:
            # 调用Anthropic API，让LLM选择工具
            response = self.anthropic.messages.create(
                model=self.model,
                max_tokens=4000,
                messages=messages,
                tools=available_tools
            )
            
            # 处理响应和工具调用
            return await self._handle_llm_response(response, messages)
            
        except Exception as e:
            logger.exception(f"Error in LLM processing: {e}")
            raise
    
    async def _handle_llm_response(self, response: Message, messages: List[Dict[str, str]]) -> str:
        """
        处理LLM响应和工具调用
        
        Args:
            response: LLM响应
            messages: 消息历史
            
        Returns:
            最终响应内容
        """
        response_parts = []
        tool_results = []
        
        # 处理响应内容
        for content in response.content:
            if isinstance(content, TextBlock):
                response_parts.append(content.text)
            elif isinstance(content, ToolUseBlock):
                # LLM选择了工具，执行工具调用
                tool_result = await self._execute_tool_call(content)
                tool_results.append(tool_result)
        
        # 如果有工具调用结果，可能需要继续对话
        if tool_results:
            return await self._handle_tool_results(response_parts, tool_results, messages)
        
        # 没有工具调用，直接返回文本响应
        return "\n".join(response_parts) if response_parts else "抱歉，我无法处理您的请求。"
    
    async def _execute_tool_call(self, tool_use: ToolUseBlock) -> Dict[str, Any]:
        """
        执行LLM选择的工具调用
        
        Args:
            tool_use: 工具使用块
            
        Returns:
            工具执行结果
        """
        try:
            logger.info(f"Executing tool: {tool_use.name} with args: {tool_use.input}")
            
            # 使用MCP客户端执行工具
            result = await self.mcp_client.call_tool(tool_use.name, tool_use.input)
            
            return {
                "tool_name": tool_use.name,
                "tool_use_id": tool_use.id,
                "success": True,
                "result": result
            }
            
        except Exception as e:
            logger.exception(f"Error executing tool {tool_use.name}: {e}")
            return {
                "tool_name": tool_use.name,
                "tool_use_id": tool_use.id,
                "success": False,
                "error": str(e)
            }
    
    async def _handle_tool_results(self, response_parts: List[str], tool_results: List[Dict[str, Any]], 
                                 messages: List[Dict[str, str]]) -> str:
        """
        处理工具执行结果
        
        Args:
            response_parts: LLM的文本响应部分
            tool_results: 工具执行结果
            messages: 消息历史
            
        Returns:
            最终响应内容
        """
        # 构建工具结果摘要
        successful_results = [r for r in tool_results if r["success"]]
        failed_results = [r for r in tool_results if not r["success"]]
        
        result_summary = []
        
        if successful_results:
            result_summary.append(f"成功执行了 {len(successful_results)} 个工具")
            for result in successful_results:
                if isinstance(result["result"], dict) and "content" in result["result"]:
                    # 提取实际内容
                    content = result["result"]["content"]
                    if isinstance(content, list) and content:
                        # 处理MCP工具返回的内容列表
                        text_content = []
                        for item in content:
                            if isinstance(item, dict) and item.get("type") == "text":
                                text_content.append(item.get("text", ""))
                        if text_content:
                            result_summary.append(f"工具 {result['tool_name']} 结果：{''.join(text_content)}")
        
        if failed_results:
            result_summary.append(f"有 {len(failed_results)} 个工具执行失败")
            for result in failed_results:
                result_summary.append(f"工具 {result['tool_name']} 失败：{result['error']}")
        
        # 组合最终响应
        final_response = []
        if response_parts:
            final_response.extend(response_parts)
        if result_summary:
            final_response.extend(result_summary)
        
        return "\n".join(final_response) if final_response else "工具执行完成，但没有返回具体结果。"
    
    def _extract_tools_used(self, response: str) -> List[str]:
        """
        从响应中提取使用的工具列表

        Args:
            response: 响应内容

        Returns:
            使用的工具名称列表
        """
        # 这里可以实现更复杂的工具提取逻辑
        # 目前返回空列表，可以根据需要扩展
        return []


class DeepSeekLLMClient(BaseLLMClient):
    """DeepSeek LLM客户端 - 实现MCP协议标准的LLM工具选择机制"""

    def __init__(self, deepseek_api_key: str, mcp_client: MCPClient):
        """
        初始化DeepSeek LLM客户端

        Args:
            deepseek_api_key: DeepSeek API密钥
            mcp_client: MCP客户端实例
        """
        super().__init__(mcp_client)
        # 初始化多阶段Prompt注入器
        self.prompt_injector = MultiStagePromptInjector()

        # 使用OpenAI兼容的客户端连接DeepSeek API
        try:
            logger.info(f"🔍 Debug - Initializing DeepSeek client with API key: {'设置' if deepseek_api_key else '未设置'}")
            from openai import OpenAI
            logger.info("🔍 Debug - OpenAI package imported successfully")
            self.client = OpenAI(
                api_key=deepseek_api_key,
                base_url="https://api.deepseek.com/v1",
                timeout=90.0,  # 设置90秒超时
                max_retries=2  # 设置重试次数
            )
            logger.info("🔍 Debug - OpenAI client created successfully")
            self.model = "deepseek-chat"
            logger.info("✅ DeepSeek LLM client initialized with 90s timeout and multi-stage prompt injector")
        except ImportError as e:
            logger.error(f"❌ OpenAI package not found: {e}. Please install: pip install openai")
            raise
        except Exception as e:
            logger.error(f"❌ Failed to initialize DeepSeek client: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            raise

    async def process_request(self, user_query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理用户请求 - MCP协议标准工作流程

        Args:
            user_query: 用户的自然语言请求
            context: 可选的上下文信息

        Returns:
            处理结果字典
        """
        tracker = ProcessingTracker()

        try:
            logger.info(f"[DEEPSEEK] Processing user request: {user_query[:100]}...")

            # 步骤1: 分析用户需求
            step1 = tracker.add_step("analyze_request", "分析用户需求")
            await asyncio.sleep(0.1)  # 模拟分析时间
            tracker.complete_step(step1)

            # 步骤2: 获取可用工具
            step2 = tracker.add_step("get_tools", "获取可用工具")
            available_tools = await self._get_available_tools()
            if not available_tools:
                tracker.fail_step(step2, "没有可用的MCP工具")
                return {
                    "success": False,
                    "error": "No MCP tools available",
                    "response": "抱歉，当前没有可用的工具来处理您的请求。",
                    "tool_calls": tracker.tool_calls,
                    "processing_steps": tracker.steps,
                    "total_duration_ms": tracker.get_total_duration()
                }
            tracker.complete_step(step2)

            # 步骤3: 准备执行参数
            step3 = tracker.add_step("prepare_execution", "准备执行参数")
            messages = self._build_messages(user_query, context)
            tracker.complete_step(step3)

            # 步骤4: 执行AI任务（细分为子步骤）
            step4 = tracker.add_step("execute_ai_task", "执行AI任务")
            response = await self._process_with_deepseek(messages, available_tools, tracker, step4)
            tracker.complete_step(step4)

            # 步骤5: 处理执行结果
            step5 = tracker.add_step("process_result", "处理执行结果")
            tools_used = self._extract_tools_used(response)
            tracker.complete_step(step5)

            # 调试日志：检查最终响应数据
            logger.info(f"🔍 Debug - 最终响应构建:")
            logger.info(f"🔍 Debug - tracker.tool_calls 长度: {len(tracker.tool_calls)}")
            logger.info(f"🔍 Debug - tracker.tool_calls 内容: {tracker.tool_calls}")
            logger.info(f"🔍 Debug - tracker.steps 长度: {len(tracker.steps)}")

            final_result = {
                "success": True,
                "response": response,
                "tools_used": tools_used,
                "tool_calls": tracker.tool_calls,
                "processing_steps": tracker.steps,
                "total_duration_ms": tracker.get_total_duration()
            }

            logger.info(f"🔍 Debug - 最终结果 tool_calls 长度: {len(final_result.get('tool_calls', []))}")
            return final_result

        except Exception as e:
            logger.exception(f"[DEEPSEEK] Error processing request: {e}")
            return {
                "success": False,
                "error": str(e),
                "response": f"处理请求时发生错误：{str(e)}"
            }

    async def process_request_stream(self, user_query: str, context: Optional[Dict[str, Any]] = None):
        """
        流式处理用户请求 - DeepSeek流式实现

        Args:
            user_query: 用户的自然语言请求
            context: 可选的上下文信息

        Yields:
            流式处理数据块
        """
        tracker = ProcessingTracker()

        try:
            logger.info(f"[DEEPSEEK STREAM] Starting streaming request: {user_query[:100]}...")

            # 步骤1: 分析用户需求
            yield {
                "type": "status",
                "message": "正在分析用户需求...",
                "progress": 0.1,
                "step": "analyze_request"
            }

            step1 = tracker.add_step("analyze_request", "分析用户需求")
            await asyncio.sleep(0.1)  # 模拟分析时间
            tracker.complete_step(step1)

            # 步骤2: 获取可用工具
            yield {
                "type": "status",
                "message": "正在获取可用工具...",
                "progress": 0.2,
                "step": "get_tools"
            }

            step2 = tracker.add_step("get_tools", "获取可用工具")
            available_tools = await self._get_available_tools()

            if not available_tools:
                tracker.fail_step(step2, "没有可用的MCP工具")
                yield {
                    "type": "error",
                    "message": "没有可用的工具来处理您的请求",
                    "error": "No MCP tools available"
                }
                return

            tracker.complete_step(step2)

            yield {
                "type": "status",
                "message": f"发现 {len(available_tools)} 个可用工具",
                "progress": 0.3,
                "step": "tools_discovered",
                "tools_count": len(available_tools)
            }

            # 步骤3: 准备执行参数
            step3 = tracker.add_step("prepare_execution", "准备执行参数")
            messages = self._build_messages(user_query, context)
            tracker.complete_step(step3)

            # 步骤4: 流式执行AI任务
            yield {
                "type": "status",
                "message": "正在调用DeepSeek API...",
                "progress": 0.4,
                "step": "calling_api"
            }

            step4 = tracker.add_step("execute_ai_task", "执行AI任务")

            # 使用流式API调用
            async for chunk in self._process_with_deepseek_stream(messages, available_tools, tracker, step4):
                yield chunk

            tracker.complete_step(step4)

            # 最终完成状态
            yield {
                "type": "complete",
                "message": "处理完成",
                "progress": 1.0,
                "tool_calls": tracker.tool_calls,
                "processing_steps": tracker.steps,
                "total_duration_ms": tracker.get_total_duration()
            }

        except Exception as e:
            logger.exception(f"[DEEPSEEK STREAM] Error in streaming request: {e}")
            yield {
                "type": "error",
                "message": f"处理请求时发生错误：{str(e)}",
                "error": str(e),
                "tool_calls": tracker.tool_calls if tracker else [],
                "processing_steps": tracker.steps if tracker else []
            }

    async def _get_available_tools(self) -> List[Dict[str, Any]]:
        """
        获取所有可用的MCP工具，转换为DeepSeek API格式

        Returns:
            格式化后的工具列表，符合DeepSeek API要求
        """
        try:
            # 强制输出调试信息
            print("🔧 [DEEPSEEK DEBUG] _get_available_tools 方法被调用")
            logger.info("[DEEPSEEK DEBUG] _get_available_tools 方法开始执行")

            # 使用MCP标准方法获取工具 (同步方法，不需要await)
            tools_data = self.mcp_client.get_available_tools()
            print(f"🔧 [DEEPSEEK DEBUG] 获取到 {len(tools_data)} 个原始工具")
            logger.info(f"[DEEPSEEK DEBUG] 获取到 {len(tools_data)} 个工具")

            # 查找 text_to_image 工具的原始定义
            for tool in tools_data:
                if tool.get('name') == 'text_to_image':
                    print(f"🔧 [DEEPSEEK DEBUG] text_to_image 原始定义: {tool}")
                    break

            # 转换为DeepSeek API格式（与OpenAI兼容）
            formatted_tools = []
            for tool_info in tools_data:
                tool_name = tool_info.get("name")
                logger.info(f"[DEEPSEEK DEBUG] 正在处理工具: {tool_name}")

                # 获取原始的 input_schema
                input_schema = tool_info.get("input_schema", {}).copy()  # 创建副本避免修改原始数据

                # 确保 input_schema 有正确的结构
                if not isinstance(input_schema, dict):
                    input_schema = {}

                # 确保有 type 字段
                if "type" not in input_schema:
                    input_schema["type"] = "object"

                # 确保有 properties 字段
                if "properties" not in input_schema:
                    input_schema["properties"] = {}

                # 获取properties的副本进行处理
                properties = input_schema.get("properties", {}).copy()

                # 关键修复：处理参数默认值和required字段
                required_fields = []

                for prop_name, prop_info in properties.items():
                    if isinstance(prop_info, dict):
                        prop_copy = prop_info.copy()

                        # 关键修复：移除所有空字符串默认值
                        if prop_copy.get("default") == "":
                            # 移除空字符串默认值，让DeepSeek知道这是必需参数
                            prop_copy.pop("default", None)
                            print(f"🔧 [DEEPSEEK DEBUG] 移除 {tool_name}.{prop_name} 的空默认值")
                            logger.info(f"[DEEPSEEK] 移除 {tool_name}.{prop_name} 的空默认值")

                        # 移除None默认值（对于字符串类型）
                        if prop_copy.get("default") is None and prop_copy.get("type") == "string":
                            prop_copy.pop("default", None)
                            print(f"🔧 [DEEPSEEK DEBUG] 移除 {tool_name}.{prop_name} 的None默认值")
                            logger.info(f"[DEEPSEEK] 移除 {tool_name}.{prop_name} 的None默认值")

                        # 🔧 关键修复：为output_directory参数提供正确的默认值，防止DeepSeek API生成错误路径
                        if prop_name == "output_directory" and tool_name in ["text_to_image", "text_to_audio", "generate_video", "music_generation", "voice_design"]:
                            # 使用PathManager获取正确的输出目录
                            from ..core.path_manager import PathManager
                            path_manager = PathManager()
                            correct_output_dir = path_manager.get_output_directory()
                            prop_copy["default"] = correct_output_dir
                            print(f"🔧 [DEEPSEEK DEBUG] 为 {tool_name}.{prop_name} 设置正确默认值: {correct_output_dir}")
                            logger.info(f"[DEEPSEEK] 为 {tool_name}.{prop_name} 设置正确默认值: {correct_output_dir}")

                        # 更新properties
                        properties[prop_name] = prop_copy

                        # 判断是否为必需参数
                        has_default = "default" in prop_copy
                        is_nullable = prop_copy.get("type") == "null" or "null" in str(prop_copy.get("type", ""))

                        # 核心业务参数判断逻辑
                        if tool_name == "text_to_image" and prop_name == "prompt":
                            # prompt是图像生成的核心参数，必须是必需的
                            required_fields.append(prop_name)
                        elif tool_name == "text_to_audio" and prop_name == "text":
                            # text是音频生成的核心参数
                            required_fields.append(prop_name)
                        elif tool_name == "generate_video" and prop_name == "prompt":
                            # prompt是视频生成的核心参数
                            required_fields.append(prop_name)
                        elif not has_default and not is_nullable:
                            # 其他没有默认值且不可为空的参数
                            # 排除明显的可选参数
                            if prop_name not in ["output_directory", "voice_id", "model", "aspect_ratio", "n", "prompt_optimizer"]:
                                required_fields.append(prop_name)

                # 更新schema
                input_schema["properties"] = properties
                input_schema["required"] = required_fields

                # 记录工具转换信息
                logger.info(f"[DEEPSEEK] 工具 {tool_name} - required字段: {required_fields}")

                formatted_tool = {
                    "type": "function",
                    "function": {
                        "name": tool_name,
                        "description": tool_info.get("description", f"Tool: {tool_name}"),
                        "parameters": input_schema
                    }
                }
                formatted_tools.append(formatted_tool)

            logger.info(f"[DEEPSEEK] Found {len(formatted_tools)} available tools")

            # 详细记录text_to_image工具的最终定义
            for tool in formatted_tools:
                if tool["function"]["name"] == "text_to_image":
                    print(f"🔧 [DEEPSEEK DEBUG] text_to_image最终定义: {json.dumps(tool, indent=2, ensure_ascii=False)}")
                    logger.info(f"[DEEPSEEK] text_to_image最终定义: {json.dumps(tool, indent=2, ensure_ascii=False)}")
                    break

            return formatted_tools

        except Exception as e:
            logger.exception(f"[DEEPSEEK] Error getting available tools: {e}")
            return []

    def _build_messages(self, user_query: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """
        构建消息历史，包含工具使用指导

        Args:
            user_query: 用户查询
            context: 上下文信息

        Returns:
            消息列表
        """
        messages = []

        # 添加系统 prompt 指导工具使用 - 使用参数生成阶段的prompt
        system_prompt = self._build_system_prompt_with_tool_guidance(context, stage="parameter_generation")
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
            # 强制调试日志
            print(f"🔧 [BUILD_MESSAGES] 使用参数生成阶段prompt，长度: {len(system_prompt)} 字符")
            logger.error(f"🔧 [BUILD_MESSAGES] 使用参数生成阶段prompt，长度: {len(system_prompt)} 字符")
            if "工具调用指导" in system_prompt:
                print("🔧 [BUILD_MESSAGES] ✅ 系统prompt包含工具调用指导")
                logger.error("🔧 [BUILD_MESSAGES] ✅ 系统prompt包含工具调用指导")
            else:
                print("🔧 [BUILD_MESSAGES] ❌ 系统prompt缺少工具调用指导")
                logger.error("🔧 [BUILD_MESSAGES] ❌ 系统prompt缺少工具调用指导")

        # 添加用户查询
        messages.append({"role": "user", "content": user_query})

        return messages

    def _build_system_prompt_with_tool_guidance(self, context: Optional[Dict[str, Any]] = None, stage: str = "request_parsing") -> str:
        """
        构建多阶段prompt注入系统 - 基于主流AI Agent框架最佳实践

        Args:
            context: 上下文信息，包含工作流程状态、用户历史等
            stage: prompt阶段 ("request_parsing", "parameter_generation", "tool_selection", "result_processing")

        Returns:
            动态生成的系统 prompt 内容
        """
        # 映射stage字符串到PromptStage枚举
        stage_mapping = {
            "request_parsing": PromptStage.REQUEST_PARSING,
            "parameter_generation": PromptStage.PARAMETER_GENERATION,
            "tool_selection": PromptStage.TOOL_SELECTION,
            "result_processing": PromptStage.RESULT_PROCESSING
        }

        prompt_stage = stage_mapping.get(stage, PromptStage.REQUEST_PARSING)

        # 确保prompt注入器有最新的工具信息
        if hasattr(self, 'available_tools') and self.available_tools:
            self.prompt_injector.update_available_tools(self.available_tools)

        # 使用多阶段Prompt注入器生成系统prompt
        return self.prompt_injector.inject_prompt(
            user_query="",  # 系统prompt不需要用户查询
            stage=prompt_stage,
            context=context or {}
        )

    # 注意：此方法已被MultiStagePromptInjector替代，保留仅用于向后兼容
    def _build_system_identity_prompt(self) -> str:
        """构建系统级身份定义prompt - 已废弃，使用MultiStagePromptInjector"""
        logger.warning("[DEPRECATED] _build_system_identity_prompt已废弃，请使用MultiStagePromptInjector")
        return "# 此方法已废弃，请使用MultiStagePromptInjector系统"

    # 注意：此方法已被MultiStagePromptInjector替代，保留仅用于向后兼容
    def _build_workflow_aware_prompt(self, context: Optional[Dict[str, Any]] = None) -> str:
        """构建工作流程感知prompt - 已废弃，使用MultiStagePromptInjector"""
        logger.warning("[DEPRECATED] _build_workflow_aware_prompt已废弃，请使用MultiStagePromptInjector")
        return "# 此方法已废弃，请使用MultiStagePromptInjector系统"

    # 注意：此方法已被MultiStagePromptInjector替代，保留仅用于向后兼容
    def _build_tool_guidance_prompt(self, context: Optional[Dict[str, Any]] = None) -> str:
        """构建工具级指导prompt - 已废弃，使用MultiStagePromptInjector"""
        logger.warning("[DEPRECATED] _build_tool_guidance_prompt已废弃，请使用MultiStagePromptInjector")
        return "# 此方法已废弃，请使用MultiStagePromptInjector系统"

    # 注意：此方法已被MultiStagePromptInjector替代，保留仅用于向后兼容
    def _build_context_aware_prompt(self, context: Optional[Dict[str, Any]] = None) -> str:
        """构建上下文感知优化prompt - 已废弃，使用MultiStagePromptInjector"""
        logger.warning("[DEPRECATED] _build_context_aware_prompt已废弃，请使用MultiStagePromptInjector")
        return "# 此方法已废弃，请使用MultiStagePromptInjector系统"

    def _detect_workflow_type(self, context: Optional[Dict[str, Any]] = None) -> str:
        """检测当前工作流程类型"""
        if not context:
            return "general"

        user_query = context.get("user_query", "").lower()

        # 基于关键词检测工作流程类型
        if any(keyword in user_query for keyword in ["剪辑", "编辑", "切割", "拼接", "时间线"]):
            return "editing"
        elif any(keyword in user_query for keyword in ["调色", "色彩", "颜色", "色温", "饱和度"]):
            return "color_grading"
        elif any(keyword in user_query for keyword in ["音频", "声音", "音效", "混音", "降噪"]):
            return "audio"
        elif any(keyword in user_query for keyword in ["特效", "合成", "fusion", "节点"]):
            return "effects"
        else:
            return "general"

    def _get_context_relevant_tools(self, context: Optional[Dict[str, Any]] = None) -> List[str]:
        """获取上下文相关的工具列表（智能推荐）"""
        if not context:
            return []

        user_query = context.get('user_query', '')

        # 使用智能工具推荐系统
        if hasattr(self, 'prompt_injector') and user_query:
            recommendations = self.prompt_injector.get_intelligent_tool_recommendations(user_query, context)
            if recommendations:
                return [rec['tool_name'] for rec in recommendations[:8]]  # 返回前8个推荐

        # 回退到传统方法
        workflow_type = self._detect_workflow_type(context)

        # 基于工作流程类型推荐工具（保持向后兼容）
        workflow_tools = {
            "editing": ["create_timeline", "add_media", "cut_clip", "add_transition"],
            "color_grading": ["adjust_color", "apply_lut", "color_match"],
            "audio": ["text_to_audio", "adjust_audio", "add_audio_effect"],
            "effects": ["add_effect", "create_fusion_comp", "add_text"],
            "general": ["text_to_image", "text_to_audio", "generate_video"]
        }

        return workflow_tools.get(workflow_type, workflow_tools["general"])

    def _build_system_message(self, context: Dict[str, Any]) -> str:
        """
        构建系统消息（保持向后兼容）

        Args:
            context: 上下文信息

        Returns:
            系统消息内容
        """
        system_parts = []

        if context.get("user_preferences"):
            system_parts.append(f"用户偏好：{context['user_preferences']}")

        if context.get("previous_context"):
            system_parts.append(f"之前的对话上下文：{context['previous_context']}")

        if system_parts:
            return "请根据以下上下文信息处理用户请求：\n" + "\n".join(system_parts)

        return ""

    async def _process_with_deepseek(self, messages: List[Dict[str, str]], available_tools: List[Dict[str, Any]], tracker: ProcessingTracker = None, parent_step_index: int = None) -> str:
        """
        使用DeepSeek处理请求并执行工具调用

        Args:
            messages: 消息历史
            available_tools: 可用工具列表

        Returns:
            最终响应内容
        """
        try:
            # 子步骤4.1: 调用DeepSeek API
            if tracker and parent_step_index is not None:
                tracker.update_step_progress(parent_step_index, 10, "正在调用DeepSeek API...")

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                tools=available_tools,
                max_tokens=4000,
                temperature=0.7
            )

            # 子步骤4.2: 处理响应和工具调用
            if tracker and parent_step_index is not None:
                tracker.update_step_progress(parent_step_index, 30, "正在分析AI响应...")

            return await self._handle_deepseek_response(response, messages, tracker, parent_step_index)

        except Exception as e:
            logger.exception(f"[DEEPSEEK] Error in LLM processing: {e}")
            raise

    async def _process_with_deepseek_stream(self, messages: List[Dict[str, str]], available_tools: List[Dict[str, Any]], tracker: ProcessingTracker = None, parent_step_index: int = None):
        """
        使用DeepSeek流式处理请求并执行工具调用

        Args:
            messages: 消息历史
            available_tools: 可用工具列表
            tracker: 处理跟踪器
            parent_step_index: 父步骤索引

        Yields:
            流式处理数据块
        """
        try:
            # 子步骤4.1: 调用DeepSeek流式API
            if tracker and parent_step_index is not None:
                tracker.update_step_progress(parent_step_index, 10, "正在调用DeepSeek流式API...")

            # 详细调试信息
            logger.info(f"[DEEPSEEK STREAM] 消息数量: {len(messages)}")
            logger.info(f"[DEEPSEEK STREAM] 系统消息: {messages[0]['content'][:300]}...")
            logger.info(f"[DEEPSEEK STREAM] 用户消息: {messages[-1]['content']}")
            logger.info(f"[DEEPSEEK STREAM] 可用工具数量: {len(available_tools) if available_tools else 0}")

            # 强制输出完整的系统prompt用于调试
            print(f"🔧 [DEEPSEEK STREAM] 完整系统prompt长度: {len(messages[0]['content'])} 字符")
            if "工具调用指导" in messages[0]['content']:
                print("🔧 [DEEPSEEK STREAM] ✅ 发送给DeepSeek的系统prompt包含工具调用指导")
            else:
                print("🔧 [DEEPSEEK STREAM] ❌ 发送给DeepSeek的系统prompt缺少工具调用指导")

            # 输出系统prompt的关键部分
            system_content = messages[0]['content']
            if "text_to_image" in system_content:
                print("🔧 [DEEPSEEK STREAM] ✅ 系统prompt包含text_to_image示例")
            else:
                print("🔧 [DEEPSEEK STREAM] ❌ 系统prompt缺少text_to_image示例")

            if available_tools:
                logger.info(f"[DEEPSEEK STREAM] 工具列表: {[tool['function']['name'] for tool in available_tools]}")
                # 详细记录 text_to_image 工具定义
                for tool in available_tools:
                    if tool['function']['name'] == 'text_to_image':
                        logger.info(f"[DEEPSEEK STREAM] text_to_image 工具定义: {json.dumps(tool, indent=2)}")

            yield {
                "type": "status",
                "message": "正在调用DeepSeek流式API...",
                "progress": 0.45,
                "step": "api_call_start"
            }

            # 使用流式API调用
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                tools=available_tools,
                stream=True,  # 启用流式响应
                max_tokens=4000,
                temperature=0.7
            )

            # 处理流式响应
            current_content = ""
            tool_calls_buffer = []
            tool_calls_dict = {}  # 用于累积增量式工具调用

            print(f"🔧 [DEEPSEEK STREAM] 开始处理流式响应，工具数量: {len(available_tools)}")
            logger.info(f"[DEEPSEEK STREAM] 开始处理流式响应，工具数量: {len(available_tools)}")

            for chunk in stream:
                choice = chunk.choices[0] if chunk.choices else None
                if not choice:
                    continue

                delta = choice.delta

                # 处理文本内容流式输出
                if delta.content:
                    content_delta = delta.content
                    current_content += content_delta

                    yield {
                        "type": "content",
                        "delta": content_delta,
                        "content": current_content,
                        "progress": 0.6,
                        "step": "content_generation"
                    }

                # 处理工具调用（增量式累积） - 修复版本
                if delta.tool_calls:
                    for tool_call_delta in delta.tool_calls:
                        # 使用index作为主键，因为DeepSeek API可能不提供id
                        tool_call_index = getattr(tool_call_delta, 'index', 0)
                        tool_call_id = getattr(tool_call_delta, 'id', None) or f"call_{tool_call_index}"

                        print(f"🔧 [DEEPSEEK STREAM] 处理工具调用delta - index: {tool_call_index}, id: {tool_call_id}")

                        # 使用index作为字典键，确保累积的一致性
                        dict_key = tool_call_index

                        # 初始化或更新工具调用
                        if dict_key not in tool_calls_dict:
                            tool_calls_dict[dict_key] = {
                                'id': tool_call_id,
                                'function': {
                                    'name': '',
                                    'arguments': ''
                                }
                            }

                            # 检测到新工具调用
                            yield {
                                "type": "tool_call_detected",
                                "tool_name": "detecting...",
                                "progress": 0.7,
                                "step": "tool_call_detected"
                            }
                            print(f"🔧 [DEEPSEEK STREAM] 初始化工具调用累积器 - index: {dict_key}")

                        # 累积工具调用信息
                        if tool_call_delta.function:
                            if tool_call_delta.function.name:
                                tool_calls_dict[dict_key]['function']['name'] = tool_call_delta.function.name
                                print(f"🔧 [DEEPSEEK STREAM] 设置工具名称: {tool_call_delta.function.name}")
                            if tool_call_delta.function.arguments:
                                tool_calls_dict[dict_key]['function']['arguments'] += tool_call_delta.function.arguments
                                print(f"🔧 [DEEPSEEK STREAM] 累积参数: {tool_call_delta.function.arguments}")
                                print(f"🔧 [DEEPSEEK STREAM] 当前累积参数: {tool_calls_dict[dict_key]['function']['arguments']}")

                                # 尝试解析JSON以检查是否完整
                                try:
                                    json.loads(tool_calls_dict[dict_key]['function']['arguments'])
                                    print(f"🔧 [DEEPSEEK STREAM] ✅ 工具 {tool_calls_dict[dict_key]['function']['name']} 参数JSON完整")
                                except json.JSONDecodeError:
                                    print(f"🔧 [DEEPSEEK STREAM] ⏳ 工具 {tool_calls_dict[dict_key]['function']['name']} 参数JSON不完整，继续累积")

                # 检查流式响应是否结束
                if choice.finish_reason:
                    logger.debug(f"[DEEPSEEK STREAM] 流式响应结束，原因: {choice.finish_reason}")
                    break

            # 将累积的工具调用转换为最终格式
            logger.info(f"[DEEPSEEK STREAM] 累积的工具调用数据: {tool_calls_dict}")

            for tool_call_data in tool_calls_dict.values():
                if tool_call_data['function']['name']:  # 只处理有名称的工具调用
                    logger.info(f"[DEEPSEEK STREAM] 处理工具调用: {tool_call_data['function']['name']}")
                    logger.info(f"[DEEPSEEK STREAM] 工具参数原始数据: {repr(tool_call_data['function']['arguments'])}")

                    # 创建工具调用对象
                    class ToolCall:
                        def __init__(self, data):
                            self.id = data['id']
                            self.function = ToolCallFunction(data['function'])

                    class ToolCallFunction:
                        def __init__(self, func_data):
                            self.name = func_data['name']
                            self.arguments = func_data['arguments']

                    tool_call = ToolCall(tool_call_data)
                    tool_calls_buffer.append(tool_call)

                    logger.debug(f"[DEEPSEEK STREAM] 完整工具调用 - 名称: {tool_call.function.name}, 参数长度: {len(tool_call.function.arguments)}")
                else:
                    logger.warning(f"[DEEPSEEK STREAM] 跳过不完整的工具调用: {tool_call_data}")

            # 如果有工具调用，执行它们
            if tool_calls_buffer:
                yield {
                    "type": "status",
                    "message": f"正在执行 {len(tool_calls_buffer)} 个工具...",
                    "progress": 0.75,
                    "step": "executing_tools"
                }

                # 执行工具调用
                async for tool_chunk in self._execute_tool_calls_stream(tool_calls_buffer, tracker):
                    yield tool_chunk

            # 发送最终内容
            if current_content:
                yield {
                    "type": "response",
                    "content": current_content,
                    "progress": 0.95,
                    "step": "response_complete"
                }

            # 发送完成事件
            yield {
                "type": "complete",
                "message": "流式处理完成",
                "progress": 1.0,
                "step": "complete"
            }
            print(f"🎉 [DEEPSEEK STREAM] 发送完成事件")
            logger.info(f"[DEEPSEEK STREAM] 发送完成事件")

        except Exception as e:
            logger.exception(f"[DEEPSEEK STREAM] Error in streaming LLM processing: {e}")
            yield {
                "type": "error",
                "message": f"流式处理过程中发生错误：{str(e)}",
                "error": str(e)
            }

    async def _execute_tool_calls_stream(self, tool_calls, tracker: ProcessingTracker = None):
        """
        流式执行工具调用

        Args:
            tool_calls: 工具调用列表
            tracker: 处理跟踪器

        Yields:
            工具执行流式数据块
        """
        for i, tool_call in enumerate(tool_calls):
            try:
                tool_name = tool_call.function.name

                # 安全解析工具参数，添加详细的错误处理和调试信息
                raw_arguments = tool_call.function.arguments
                logger.debug(f"[DEEPSEEK STREAM] 解析工具参数 - 工具: {tool_name}, 原始参数: {repr(raw_arguments)}")

                # 检查参数是否为空或None
                if not raw_arguments or raw_arguments.strip() == "":
                    error_msg = f"DeepSeek API 返回了空的工具参数。工具: {tool_name}"
                    logger.error(f"[DEEPSEEK STREAM] {error_msg}")
                    logger.error(f"[DEEPSEEK STREAM] 这表明 LLM 没有正确理解如何使用工具")
                    logger.error(f"[DEEPSEEK STREAM] 请检查系统 prompt 和工具定义")

                    # 发送错误状态
                    yield {
                        "type": "tool_call_error",
                        "tool_name": tool_name,
                        "error": error_msg,
                        "progress": 0.8 + (0.15 * i / len(tool_calls)),
                        "step": f"tool_{i+1}_parameter_error"
                    }
                    continue
                else:
                    try:
                        # 尝试解析JSON参数
                        tool_args = json.loads(raw_arguments)
                        logger.debug(f"[DEEPSEEK STREAM] 成功解析工具参数: {tool_args}")
                    except json.JSONDecodeError as json_error:
                        logger.error(f"[DEEPSEEK STREAM] JSON解析失败 - 工具: {tool_name}, 错误: {json_error}")
                        logger.error(f"[DEEPSEEK STREAM] 原始参数内容: {repr(raw_arguments)}")
                        logger.error(f"[DEEPSEEK STREAM] 参数长度: {len(raw_arguments)}, 类型: {type(raw_arguments)}")

                        # 尝试修复常见的JSON格式问题
                        try:
                            # 移除可能的前后空白字符
                            cleaned_args = raw_arguments.strip()
                            if cleaned_args:
                                tool_args = json.loads(cleaned_args)
                                logger.info(f"[DEEPSEEK STREAM] 清理后成功解析参数: {tool_args}")
                            else:
                                raise json.JSONDecodeError("Empty string after cleaning", "", 0)
                        except json.JSONDecodeError:
                            error_msg = f"无法解析工具参数 JSON。工具: {tool_name}, 原始参数: {raw_arguments}"
                            logger.error(f"[DEEPSEEK STREAM] {error_msg}")

                            # 发送错误状态
                            yield {
                                "type": "tool_call_error",
                                "tool_name": tool_name,
                                "error": error_msg,
                                "progress": 0.8 + (0.15 * i / len(tool_calls)),
                                "step": f"tool_{i+1}_json_error"
                            }
                            continue

                # 确保tool_args是字典类型
                if not isinstance(tool_args, dict):
                    logger.warning(f"[DEEPSEEK STREAM] 工具参数不是字典类型，转换为字典: {type(tool_args)} -> dict")
                    tool_args = {"value": tool_args}

                # 开始执行工具
                yield {
                    "type": "tool_execution_start",
                    "tool_name": tool_name,
                    "arguments": tool_args,
                    "progress": 0.8 + (0.15 * i / len(tool_calls)),
                    "step": f"executing_tool_{i+1}"
                }

                # 使用流式MCP工具调用
                tool_success = False
                tool_result = None
                tool_error = None

                async for mcp_chunk in self.mcp_client.call_tool_stream(tool_name, tool_args):
                    # 转发MCP流式状态
                    yield {
                        "type": "mcp_status",
                        "mcp_type": mcp_chunk.get("type"),
                        "tool_name": tool_name,
                        "message": mcp_chunk.get("message", ""),
                        "progress": 0.8 + (0.15 * i / len(tool_calls)) + (0.1 * (i + 1) / len(tool_calls)),
                        "step": f"tool_{i+1}_mcp_{mcp_chunk.get('type', 'unknown')}"
                    }

                    # 检查是否完成
                    if mcp_chunk.get("type") == "tool_call_complete":
                        tool_success = mcp_chunk.get("success", False)
                        tool_result = mcp_chunk.get("result")
                        break
                    elif mcp_chunk.get("type") in ["tool_call_error", "all_servers_failed"]:
                        tool_success = False
                        tool_error = mcp_chunk.get("error", "Unknown error")
                        break

                # 记录工具调用到tracker
                if tracker:
                    try:
                        if hasattr(tracker, 'add_tool_call_stream'):
                            await tracker.add_tool_call_stream(
                                name=tool_name,
                                server="mcp_server",
                                arguments=tool_args,
                                result=tool_result,
                                success=tool_success,
                                error=tool_error,
                                duration_ms=0
                            )
                        else:
                            tracker.add_tool_call(
                                name=tool_name,
                                server="mcp_server",
                                arguments=tool_args,
                                result=tool_result,
                                success=tool_success,
                                error=tool_error,
                                duration_ms=0
                            )
                    except Exception as e:
                        logger.error(f"❌ 工具调用记录失败: {e}")

                # 发送工具执行完成
                yield {
                    "type": "tool_execution_complete",
                    "tool_name": tool_name,
                    "server": "mcp_server",
                    "arguments": tool_args,
                    "result": tool_result,
                    "success": tool_success,
                    "error": tool_error,
                    "duration_ms": 0,  # MCP已经处理了时间计算
                    "progress": 0.8 + (0.15 * (i + 1) / len(tool_calls)),
                    "step": f"tool_{i+1}_complete"
                }

            except Exception as e:
                # 获取工具名称，即使在解析参数时失败
                tool_name = getattr(tool_call.function, 'name', 'unknown_tool')
                error_msg = str(e)

                # 特殊处理JSON解析错误
                if isinstance(e, json.JSONDecodeError):
                    error_msg = f"JSON参数解析失败: {error_msg}"
                    logger.error(f"[DEEPSEEK STREAM] JSON解析错误详情 - 工具: {tool_name}")
                    logger.error(f"[DEEPSEEK STREAM] 原始参数: {repr(getattr(tool_call.function, 'arguments', 'N/A'))}")

                logger.exception(f"[DEEPSEEK STREAM] 工具执行错误 - 工具: {tool_name}, 错误: {error_msg}")

                yield {
                    "type": "tool_execution_error",
                    "tool_name": tool_name,
                    "error": error_msg,
                    "success": False,
                    "progress": 0.8 + (0.15 * (i + 1) / len(tool_calls)),
                    "step": f"tool_{i+1}_error",
                    "debug_info": {
                        "error_type": type(e).__name__,
                        "raw_arguments": getattr(tool_call.function, 'arguments', 'N/A') if hasattr(tool_call, 'function') else 'N/A'
                    }
                }

    async def _handle_deepseek_response(self, response, messages: List[Dict[str, str]], tracker: ProcessingTracker = None, parent_step_index: int = None) -> str:
        """
        处理DeepSeek响应和工具调用

        Args:
            response: DeepSeek响应
            messages: 消息历史

        Returns:
            最终响应内容
        """
        message = response.choices[0].message
        response_parts = []
        tool_results = []

        # 处理文本响应
        if message.content:
            response_parts.append(message.content)

        # 处理工具调用
        if message.tool_calls:
            # 子步骤4.3: 执行工具调用
            if tracker and parent_step_index is not None:
                tracker.update_step_progress(parent_step_index, 50, f"正在执行 {len(message.tool_calls)} 个工具...")

            for i, tool_call in enumerate(message.tool_calls):
                # 更新工具执行进度
                if tracker and parent_step_index is not None:
                    progress = 50 + (30 * (i + 1) / len(message.tool_calls))
                    tracker.update_step_progress(parent_step_index, progress, f"正在执行工具 {tool_call.function.name}...")

                tool_result = await self._execute_deepseek_tool_call(tool_call)
                tool_results.append(tool_result)

                # 记录工具调用到tracker
                if tracker:
                    logger.info(f"🔍 Debug - 准备记录工具调用: {tool_call.function.name}")
                    logger.info(f"🔍 Debug - tracker.tool_calls 当前长度: {len(tracker.tool_calls)}")
                    try:
                        tracker.add_tool_call(
                            name=tool_call.function.name,
                            server="mcp_server",  # 添加缺失的server参数
                            arguments=json.loads(tool_call.function.arguments),
                            result=tool_result.get("result"),
                            success=tool_result.get("success", False),
                            error=tool_result.get("error"),
                            duration_ms=0  # 这里可以添加实际的执行时间测量
                        )
                        logger.info(f"✅ Debug - 工具调用记录成功，tracker.tool_calls 新长度: {len(tracker.tool_calls)}")
                    except Exception as e:
                        logger.error(f"❌ Debug - 工具调用记录失败: {e}")
                        import traceback
                        logger.error(f"❌ Debug - 详细错误: {traceback.format_exc()}")

        # 如果有工具调用结果，可能需要继续对话
        if tool_results:
            # 子步骤4.4: 处理工具结果
            if tracker and parent_step_index is not None:
                tracker.update_step_progress(parent_step_index, 85, "正在处理工具执行结果...")

            return await self._handle_deepseek_tool_results(response_parts, tool_results, messages)

        # 没有工具调用，直接返回文本响应
        return "\n".join(response_parts) if response_parts else "抱歉，我无法处理您的请求。"

    async def _execute_deepseek_tool_call(self, tool_call) -> Dict[str, Any]:
        """
        执行DeepSeek选择的工具调用

        Args:
            tool_call: DeepSeek工具调用对象

        Returns:
            工具执行结果
        """
        try:
            tool_name = tool_call.function.name
            tool_args = json.loads(tool_call.function.arguments)

            logger.info(f"[DEEPSEEK] Executing tool: {tool_name} with args: {tool_args}")

            # 使用MCP客户端执行工具
            result = await self.mcp_client.call_tool(tool_name, tool_args)

            return {
                "tool_name": tool_name,
                "tool_call_id": tool_call.id,
                "success": True,
                "result": result
            }

        except Exception as e:
            logger.exception(f"[DEEPSEEK] Error executing tool {tool_call.function.name}: {e}")
            return {
                "tool_name": tool_call.function.name,
                "tool_call_id": tool_call.id,
                "success": False,
                "error": str(e)
            }

    async def _handle_deepseek_tool_results(self, response_parts: List[str], tool_results: List[Dict[str, Any]],
                                          messages: List[Dict[str, str]]) -> str:
        """
        处理DeepSeek工具执行结果

        Args:
            response_parts: LLM的文本响应部分
            tool_results: 工具执行结果
            messages: 消息历史

        Returns:
            最终响应内容
        """
        # 构建工具结果摘要
        successful_results = [r for r in tool_results if r["success"]]
        failed_results = [r for r in tool_results if not r["success"]]

        result_summary = []

        if successful_results:
            logger.info(f"[DEEPSEEK] Processing {len(successful_results)} successful tool results")

            for result in successful_results:
                tool_name = result.get('tool_name', 'unknown')
                tool_result = result.get('result', {})

                logger.debug(f"[DEEPSEEK] Processing result for tool {tool_name}: {tool_result}")

                # 处理不同类型的工具结果
                if isinstance(tool_result, list):
                    # 直接是内容列表
                    content_parts = []
                    for item in tool_result:
                        if isinstance(item, dict):
                            if item.get("type") == "text":
                                content_parts.append(item.get("text", ""))
                            elif item.get("type") == "image":
                                content_parts.append(f"[图像: {item.get('mimeType', 'unknown')}]")
                            elif item.get("type") == "resource":
                                resource = item.get("resource", {})
                                content_parts.append(f"[资源: {resource.get('uri', 'unknown')}]")

                    if content_parts:
                        result_summary.append(f"✅ {tool_name} 执行成功：")
                        result_summary.append("".join(content_parts))

                elif isinstance(tool_result, dict):
                    # 检查是否有嵌套的content字段
                    if "content" in tool_result:
                        content = tool_result["content"]
                        if isinstance(content, list):
                            content_parts = []
                            for item in content:
                                if isinstance(item, dict) and item.get("type") == "text":
                                    content_parts.append(item.get("text", ""))
                            if content_parts:
                                result_summary.append(f"✅ {tool_name} 执行成功：")
                                result_summary.append("".join(content_parts))
                        else:
                            result_summary.append(f"✅ {tool_name} 执行成功：{str(content)}")
                    else:
                        # 直接显示整个结果
                        result_summary.append(f"✅ {tool_name} 执行成功：{str(tool_result)}")

                else:
                    # 其他类型，直接转换为字符串
                    result_summary.append(f"✅ {tool_name} 执行成功：{str(tool_result)}")

        if failed_results:
            result_summary.append(f"❌ 有 {len(failed_results)} 个工具执行失败：")
            for result in failed_results:
                tool_name = result.get('tool_name', 'unknown')
                error = result.get('error', 'Unknown error')
                result_summary.append(f"  • {tool_name}: {error}")

        # 组合最终响应
        final_response = []
        if response_parts:
            final_response.extend(response_parts)
        if result_summary:
            final_response.extend(result_summary)

        final_result = "\n".join(final_response) if final_response else "工具执行完成，但没有返回具体结果。"
        logger.info(f"[DEEPSEEK] Final response: {final_result[:200]}...")

        return final_result

    def _extract_tools_used(self, response: str) -> List[str]:
        """
        从响应中提取使用的工具列表

        Args:
            response: 响应内容

        Returns:
            使用的工具名称列表
        """
        # 这里可以实现更复杂的工具提取逻辑
        # 目前返回空列表，可以根据需要扩展
        return []


# LLM客户端工厂
class LLMClientFactory:
    """LLM客户端工厂 - 根据配置创建不同的LLM客户端"""

    @staticmethod
    def create_client(provider: str, mcp_client: MCPClient, **kwargs) -> BaseLLMClient:
        """
        创建LLM客户端

        Args:
            provider: LLM提供商 ("anthropic" 或 "deepseek")
            mcp_client: MCP客户端实例
            **kwargs: 其他参数（如API密钥）

        Returns:
            LLM客户端实例
        """
        provider = provider.lower()

        if provider == "anthropic":
            api_key = kwargs.get("anthropic_api_key")
            if not api_key:
                raise ValueError("Anthropic API key is required")
            return AnthropicLLMClient(api_key, mcp_client)

        elif provider == "deepseek":
            api_key = kwargs.get("deepseek_api_key")
            if not api_key:
                raise ValueError("DeepSeek API key is required")
            return DeepSeekLLMClient(api_key, mcp_client)

        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")


# 为了向后兼容，保留原有的LLMClient类名
LLMClient = AnthropicLLMClient
