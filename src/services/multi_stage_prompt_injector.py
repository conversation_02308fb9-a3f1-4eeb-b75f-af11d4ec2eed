"""
DaVinci AI Co-pilot Pro 多阶段Prompt注入架构
基于主流AI Agent框架最佳实践设计，专为TVC剪辑师优化

参考框架：
- Manus AI的Context Engineering
- CrewAI的多代理协作架构
- LangChain/LangGraph的编排系统
- AutoGPT的自主任务完成

TVC专业优化：
- 支持复杂motionboard制作流程
- 智能工具推荐和工具链组合
- 专业TVC工作流程感知
"""

from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import json
import logging
from datetime import datetime

# 导入TVC专业模块
from .tvc_workflow_manager import TVCWorkflowManager, TVCWorkflowType
from .intelligent_tool_recommender import IntelligentToolRecommender

logger = logging.getLogger(__name__)

class WorkflowType(Enum):
    """DaVinci Resolve工作流程类型"""
    EDITING = "editing"
    COLOR_GRADING = "color_grading"
    AUDIO = "audio"
    EFFECTS = "effects"
    GENERAL = "general"

class PromptStage(Enum):
    """Prompt注入阶段"""
    REQUEST_PARSING = "request_parsing"
    TOOL_SELECTION = "tool_selection"
    PARAMETER_GENERATION = "parameter_generation"
    RESULT_PROCESSING = "result_processing"

class MultiStagePromptInjector:
    """
    多阶段Prompt注入器 - TVC专业版

    实现动态、上下文感知的prompt生成系统，支持：
    1. 多阶段prompt注入
    2. 上下文感知prompt调整
    3. 分层prompt架构
    4. 迭代式prompt优化
    5. TVC专业工作流程支持
    6. 智能工具推荐系统
    """

    def __init__(self):
        self.conversation_history = []
        self.user_preferences = {}
        self.error_recovery_context = {}
        self.workflow_context = {}

        # TVC专业模块
        self.tvc_workflow_manager = TVCWorkflowManager()
        self.tool_recommender = IntelligentToolRecommender()
        self.available_tools = []
        
    def inject_prompt(self, 
                     user_query: str, 
                     stage: PromptStage,
                     context: Optional[Dict[str, Any]] = None) -> str:
        """
        主要的prompt注入方法
        
        Args:
            user_query: 用户查询
            stage: 当前处理阶段
            context: 上下文信息
            
        Returns:
            动态生成的prompt
        """
        # 更新上下文
        full_context = self._build_full_context(user_query, context)
        
        # 根据阶段生成对应的prompt
        stage_prompts = {
            PromptStage.REQUEST_PARSING: self._build_request_parsing_prompt,
            PromptStage.TOOL_SELECTION: self._build_tool_selection_prompt,
            PromptStage.PARAMETER_GENERATION: self._build_parameter_generation_prompt,
            PromptStage.RESULT_PROCESSING: self._build_result_processing_prompt
        }
        
        stage_prompt = stage_prompts[stage](full_context)
        
        # 组合完整的prompt
        return self._combine_prompts(
            system_prompt=self._build_system_identity_prompt(),
            workflow_prompt=self._build_workflow_aware_prompt(full_context),
            stage_prompt=stage_prompt,
            context_prompt=self._build_context_aware_prompt(full_context)
        )
    
    def _build_full_context(self, user_query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """构建完整的上下文信息"""
        full_context = {
            "user_query": user_query,
            "timestamp": datetime.now().isoformat(),
            "conversation_history": self.conversation_history[-5:],  # 保留最近5轮对话
            "user_preferences": self.user_preferences,
            "error_recovery": self.error_recovery_context,
            "workflow_context": self.workflow_context
        }
        
        if context:
            full_context.update(context)
            
        return full_context
    
    def _build_system_identity_prompt(self) -> str:
        """系统级身份定义prompt - TVC专业版"""
        return """你是 DaVinci AI Co-pilot Pro，专为TVC剪辑师打造的专业AI助手。

🎯 **专业身份**
- 资深TVC制作总监，拥有丰富的广告制作经验
- DaVinci Resolve专家级用户，精通全流程制作
- Motionboard制作专家，擅长分镜视觉化
- 自动化工作流程设计师，提升制作效率

🧠 **核心专长**
- **Motionboard制作**: 从草图到精美视觉稿的完整流程
- **脚本解析**: 智能读取Excel/PDF脚本，理解分镜需求
- **图像生成**: 将文字描述转换为专业TVC视觉稿
- **音频制作**: 字幕提取、语音合成、音效设计
- **自动化剪辑**: 批量处理、智能匹配、精确时长控制
- **工具链优化**: 从90+个DaVinci工具中智能选择最佳组合

🎬 **TVC制作理念**
- 保持广告行业的专业标准和美学要求
- 注重创意表达与技术实现的完美结合
- 追求高效率的自动化制作流程
- 确保每个细节都符合播出标准"""

    def _build_workflow_aware_prompt(self, context: Dict[str, Any]) -> str:
        """工作流程感知prompt - TVC专业版"""
        # 首先检测是否为TVC专业工作流程
        tvc_workflow = self.tvc_workflow_manager.detect_tvc_workflow(
            context.get('user_query', ''), context
        )

        # 如果是TVC专业工作流程，使用专业prompt
        if tvc_workflow != TVCWorkflowType.AUTO_EDITING:
            return self.tvc_workflow_manager.build_tvc_workflow_prompt(tvc_workflow, context)

        # 否则使用通用工作流程检测
        workflow_type = self._detect_workflow_type(context)

        workflow_prompts = {
            WorkflowType.EDITING: """🎬 **视频编辑专家模式**
当前专注于：剪辑、拼接、转场效果
优先考虑：时间线操作、素材管理、剪辑精度
专业建议：保持剪辑节奏、注意音画同步""",

            WorkflowType.COLOR_GRADING: """🎨 **调色专家模式**
当前专注于：色彩校正、调色创意、视觉风格
优先考虑：色彩空间、监看环境、调色工具
专业建议：保持肤色自然、注意色彩一致性""",

            WorkflowType.AUDIO: """🔊 **音频专家模式**
当前专注于：音频编辑、混音、音效处理
优先考虑：音频质量、电平控制、空间感
专业建议：保持音频清晰、注意动态范围""",

            WorkflowType.EFFECTS: """✨ **特效专家模式**
当前专注于：视觉特效、合成、动画制作
优先考虑：节点连接、渲染优化、创意实现
专业建议：保持效果自然、注意性能优化""",

            WorkflowType.GENERAL: """🔧 **全能助手模式**
当前状态：准备协助各种DaVinci Resolve任务
能力范围：编辑、调色、音频、特效全流程
工作方式：根据具体需求动态调整专业模式"""
        }

        return workflow_prompts[workflow_type]
    
    def _build_request_parsing_prompt(self, context: Dict[str, Any]) -> str:
        """请求解析阶段prompt"""
        return """🔍 **请求解析阶段**

你的任务是准确理解用户的意图和需求：

1. **意图识别**：
   - 用户想要完成什么任务？
   - 这是一个技术问题还是创意需求？
   - 需要哪些具体的操作步骤？

2. **需求分析**：
   - 提取关键信息和参数
   - 识别可能的歧义或不明确之处
   - 确定所需的工具和资源

3. **上下文关联**：
   - 结合之前的对话历史
   - 考虑用户的工作流程状态
   - 关联相关的项目信息

如果用户的请求不够明确，主动询问关键细节。"""

    def _build_tool_selection_prompt(self, context: Dict[str, Any]) -> str:
        """工具选择阶段prompt"""
        available_tools = self._get_context_relevant_tools(context)
        
        prompt = """🛠️ **工具选择阶段**

基于用户需求，选择最合适的工具：

1. **工具评估**：
   - 哪些工具能够完成用户的任务？
   - 哪个工具最适合当前的工作流程？
   - 是否需要组合多个工具？

2. **效率优化**：
   - 选择最直接有效的工具
   - 考虑工具的执行时间和资源消耗
   - 优先使用用户熟悉的工具

3. **多模态能力识别**：
   - 如果用户提到图片、视频帧、视觉分析，优先考虑OpenRouter MCP的chat_completion
   - 需要模型选择时，使用search_models工具
   - 图像URL格式：支持网络图片直接分析"""
        
        if available_tools:
            tool_list = "\n".join([f"   - {tool}" for tool in available_tools])
            prompt += f"\n\n🎯 **推荐工具**：\n{tool_list}"
            
        return prompt
    
    def _build_parameter_generation_prompt(self, context: Dict[str, Any]) -> str:
        """参数生成阶段prompt - TVC专业版"""

        # 获取智能工具推荐
        user_query = context.get('user_query', '')
        recommended_tools = []

        if self.available_tools and user_query:
            recommendations = self.tool_recommender.recommend_tools_for_intent(
                user_query, self.available_tools, context, max_recommendations=5
            )
            recommended_tools = [rec['tool_name'] for rec in recommendations]

        base_prompt = """⚙️ **TVC专业参数生成阶段**

为选定的工具生成准确的参数：

1. **参数完整性**：
   - 确保所有必需参数都有值
   - 绝不能生成空参数 {}
   - 每个参数都要有具体的内容

2. **TVC专业标准**：
   - 图像生成：符合广告视觉标准，注重构图和色彩
   - 视频生成：考虑TVC的节奏感和视觉冲击力
   - 音频合成：选择适合品牌调性的声音特征
   - 时长控制：精确到帧的时长匹配

3. **智能推断**：
   - 基于TVC制作经验提供专业默认值
   - 考虑广告行业的标准规范
   - 结合用户的制作偏好

⚠️ **关键提醒**：参数必须基于用户的实际输入，符合TVC制作标准。

🎬 **TVC专业工具调用指导**：
- **text_to_image**: {"prompt": "professional advertising storyboard, clean composition, commercial quality, 16:9 aspect ratio"}
- **text_to_audio**: {"text": "专业广告配音文本", "voice_id": "professional_narrator", "speed": 1.0}
- **generate_video**: {"prompt": "cinematic commercial style, high quality, professional lighting"}
- **分镜图生图**: {"prompt": "professional storyboard style, clean composition, advertising quality"}
- **motionboard优化**: {"style": "commercial", "quality": "broadcast", "aspect_ratio": "16:9"}
- **语音合成**: {"voice_type": "professional", "emotion": "engaging", "speed": "natural"}
- **视频生成**: {"style": "cinematic", "duration": "precise_timing", "quality": "4K"}

👁️ **多模态视觉分析能力** (OpenRouter MCP):
当用户需要分析图片、视频帧或视觉内容时，使用chat_completion工具：
- **图片分析**: {"model": "qwen/qwen2.5-vl-72b-instruct:free", "messages": [{"role": "user", "content": [{"type": "text", "text": "分析描述"}, {"type": "image_url", "image_url": {"url": "图片URL"}}]}], "max_tokens": 500}
- **视觉构图评价**: 专业摄影角度分析构图、光线、色彩
- **场景理解**: 识别物体、环境、情绪氛围
- **创意指导**: 提供视觉改进建议和专业评价
- **完全免费**: 使用免费VL模型，支持中文，无额外成本

🔍 **智能模型选择** (OpenRouter MCP):
- **search_models**: 根据需求搜索最适合的AI模型 {"query": "vision", "capabilities": {"vision": true}, "limit": 5}
- **get_model_info**: 获取模型详细信息 {"model": "qwen/qwen2.5-vl-72b-instruct:free"}
- **validate_model**: 验证模型可用性 {"model": "模型ID"}"""

        # 添加智能推荐工具信息
        if recommended_tools:
            tool_list = "\n".join([f"   - {tool}" for tool in recommended_tools])
            base_prompt += f"\n\n🎯 **当前推荐工具**：\n{tool_list}"

        return base_prompt

    def _build_result_processing_prompt(self, context: Dict[str, Any]) -> str:
        """结果处理阶段prompt"""
        return """📊 **结果处理阶段**

处理工具执行结果并提供用户反馈：

1. **结果验证**：
   - 检查工具执行是否成功
   - 验证输出结果的质量
   - 识别可能的问题或错误

2. **用户反馈**：
   - 提供清晰的执行结果说明
   - 解释完成的操作和效果
   - 给出后续建议或优化提示

3. **错误处理**：
   - 如果执行失败，分析原因
   - 提供解决方案或替代方案
   - 记录错误信息用于学习改进"""

    def _build_context_aware_prompt(self, context: Dict[str, Any]) -> str:
        """上下文感知prompt"""
        context_parts = []

        # 用户偏好
        if context.get("user_preferences"):
            prefs = context["user_preferences"]
            context_parts.append(f"📋 **用户偏好**：{prefs}")

        # 项目上下文
        if context.get("project_context"):
            project = context["project_context"]
            context_parts.append(f"🎬 **项目信息**：{project}")

        # 错误恢复 - 增强错误恢复逻辑
        if context.get("error_recovery") or context.get("previous_errors"):
            error_info = context.get("error_recovery") or context.get("previous_errors")
            context_parts.append(f"⚠️ **错误恢复**：注意避免之前的问题 - {error_info}")

        # 检查内部错误恢复上下文
        if self.error_recovery_context:
            recent_errors = list(self.error_recovery_context.values())[-2:]  # 最近2个错误
            if recent_errors:
                error_summary = "; ".join([f"{err.get('error', '未知错误')}" for err in recent_errors])
                context_parts.append(f"⚠️ **注意事项**：之前遇到的问题 - {error_summary}，请避免重复")

        # 对话历史摘要
        if context.get("conversation_history"):
            history = context["conversation_history"]
            if history:
                context_parts.append(f"💭 **对话上下文**：基于之前{len(history)}轮对话的上下文")

        return "\n\n".join(context_parts) if context_parts else ""
    
    def _detect_workflow_type(self, context: Dict[str, Any]) -> WorkflowType:
        """检测工作流程类型"""
        user_query = context.get("user_query", "").lower()
        
        # 关键词映射
        keyword_mapping = {
            WorkflowType.EDITING: ["剪辑", "编辑", "切割", "拼接", "时间线", "素材", "剪切"],
            WorkflowType.COLOR_GRADING: ["调色", "色彩", "颜色", "色温", "饱和度", "对比度", "亮度"],
            WorkflowType.AUDIO: ["音频", "声音", "音效", "混音", "降噪", "音量", "声道"],
            WorkflowType.EFFECTS: ["特效", "合成", "fusion", "节点", "动画", "转场", "滤镜"]
        }
        
        for workflow_type, keywords in keyword_mapping.items():
            if any(keyword in user_query for keyword in keywords):
                return workflow_type
                
        return WorkflowType.GENERAL
    
    def _get_context_relevant_tools(self, context: Dict[str, Any]) -> List[str]:
        """获取上下文相关工具（动态工具掩码）"""
        workflow_type = self._detect_workflow_type(context)
        
        workflow_tools = {
            WorkflowType.EDITING: [
                "create_timeline", "add_media", "cut_clip", "add_transition",
                "adjust_speed", "create_multicam", "sync_audio"
            ],
            WorkflowType.COLOR_GRADING: [
                "adjust_color", "apply_lut", "color_match", "create_power_window",
                "adjust_curves", "color_warper"
            ],
            WorkflowType.AUDIO: [
                "text_to_audio", "adjust_audio", "add_audio_effect", "noise_reduction",
                "audio_sync", "fairlight_mixer"
            ],
            WorkflowType.EFFECTS: [
                "add_effect", "create_fusion_comp", "add_text", "create_title",
                "add_generator", "motion_blur"
            ],
            WorkflowType.GENERAL: [
                "text_to_image", "text_to_audio", "generate_video", "create_project",
                "export_timeline", "render_queue"
            ]
        }
        
        return workflow_tools.get(workflow_type, workflow_tools[WorkflowType.GENERAL])
    
    def _combine_prompts(self, system_prompt: str, workflow_prompt: str, 
                        stage_prompt: str, context_prompt: str) -> str:
        """组合多层级prompt"""
        prompts = [system_prompt, workflow_prompt, stage_prompt]
        
        if context_prompt:
            prompts.append(context_prompt)
            
        return "\n\n".join(prompts)
    
    def update_conversation_history(self, user_query: str, assistant_response: str):
        """更新对话历史"""
        self.conversation_history.append({
            "timestamp": datetime.now().isoformat(),
            "user": user_query,
            "assistant": assistant_response
        })
        
        # 保持历史记录在合理范围内
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
    
    def update_user_preferences(self, preferences: Dict[str, Any]):
        """更新用户偏好"""
        self.user_preferences.update(preferences)
    
    def record_error(self, error_info: Dict[str, Any]):
        """记录错误信息用于恢复"""
        error_key = f"error_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.error_recovery_context[error_key] = error_info

        # 保持错误记录在合理范围内
        if len(self.error_recovery_context) > 5:
            oldest_key = min(self.error_recovery_context.keys())
            del self.error_recovery_context[oldest_key]

    def update_available_tools(self, tools: List[Dict[str, Any]]):
        """更新可用工具列表"""
        self.available_tools = tools
        self.tvc_workflow_manager.update_available_tools([tool.get('name', '') for tool in tools])
        logger.info(f"[PROMPT_INJECTOR] 更新可用工具: {len(tools)} 个")

    def get_intelligent_tool_recommendations(self, user_query: str, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """获取智能工具推荐"""
        if not self.available_tools:
            return []

        return self.tool_recommender.recommend_tools_for_intent(
            user_query, self.available_tools, context, max_recommendations=10
        )

    def get_tool_chain_recommendations(self, user_query: str) -> List[List[str]]:
        """获取工具链推荐"""
        if not self.available_tools:
            return []

        return self.tool_recommender.get_tool_chain_recommendation(user_query, self.available_tools)
