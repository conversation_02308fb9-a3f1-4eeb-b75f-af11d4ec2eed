"""
智能工具推荐系统
基于实际可用工具、用户意图和工作流程动态推荐最佳工具组合
"""

from typing import Dict, List, Optional, Any, Tuple
import re
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ToolCategory(object):
    """工具分类"""
    TIMELINE = "timeline"
    MEDIA = "media"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    PROJECT = "project"
    EXPORT = "export"
    ANALYSIS = "analysis"
    MULTIMODAL = "multimodal"  # 新增多模态分类

class IntelligentToolRecommender:
    """智能工具推荐器"""
    
    def __init__(self):
        self.tool_capabilities = {}
        self.user_preferences = {}
        self.usage_history = {}
        
    def analyze_available_tools(self, available_tools: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """分析可用工具并分类"""
        categorized_tools = {
            ToolCategory.TIMELINE: [],
            ToolCategory.MEDIA: [],
            ToolCategory.IMAGE: [],
            ToolCategory.VIDEO: [],
            ToolCategory.AUDIO: [],
            ToolCategory.PROJECT: [],
            ToolCategory.EXPORT: [],
            ToolCategory.ANALYSIS: [],
            ToolCategory.MULTIMODAL: []  # 添加多模态类别
        }
        
        for tool in available_tools:
            tool_name = tool.get('name', '')
            description = tool.get('description', '').lower()
            
            # 基于工具名称和描述进行智能分类
            categories = self._categorize_tool(tool_name, description)
            
            for category in categories:
                if category in categorized_tools:
                    categorized_tools[category].append(tool_name)
                    
            # 存储工具能力信息
            self.tool_capabilities[tool_name] = {
                'description': description,
                'categories': categories,
                'parameters': tool.get('parameters', {}),
                'complexity': self._assess_tool_complexity(tool)
            }
        
        return categorized_tools
    
    def _categorize_tool(self, tool_name: str, description: str) -> List[str]:
        """智能工具分类"""
        categories = []
        # 时间线相关
        timeline_keywords = ['timeline', 'sequence', 'track', 'clip', 'edit', '时间线', '序列', '轨道', '剪辑', '视频', '分辨率', '帧率']
        if any(keyword in tool_name.lower() or keyword in description for keyword in timeline_keywords):
            categories.append(ToolCategory.TIMELINE)
        
        # 媒体管理
        media_keywords = ['media', 'import', 'export', 'file', 'asset', '媒体', '导入', '导出', '文件', '素材']
        if any(keyword in tool_name.lower() or keyword in description for keyword in media_keywords):
            categories.append(ToolCategory.MEDIA)
        
        # 图像处理
        image_keywords = ['image', 'picture', 'photo', 'frame', 'still', '图像', '图片', '照片', '帧', '静帧', '分辨率']
        if any(keyword in tool_name.lower() or keyword in description for keyword in image_keywords):
            categories.append(ToolCategory.IMAGE)
        
        # 视频处理
        video_keywords = ['video', 'clip', 'footage', 'render', '视频', '片段', '素材', '渲染']
        if any(keyword in tool_name.lower() or keyword in description for keyword in video_keywords):
            categories.append(ToolCategory.VIDEO)
        
        # 音频处理
        audio_keywords = ['audio', 'sound', 'music', 'voice', 'speech', '音频', '声音', '音乐', '语音', '配音']
        if any(keyword in tool_name.lower() or keyword in description for keyword in audio_keywords):
            categories.append(ToolCategory.AUDIO)
        
        # 项目管理
        project_keywords = ['project', 'create', 'new', 'setup', '项目', '创建', '新建', '设置']
        if any(keyword in tool_name.lower() or keyword in description for keyword in project_keywords):
            categories.append(ToolCategory.PROJECT)
        
        # 分析工具
        analysis_keywords = ['analyze', 'detect', 'recognize', 'extract', '分析', '检测', '识别', '提取']
        if any(keyword in tool_name.lower() or keyword in description for keyword in analysis_keywords):
            categories.append(ToolCategory.ANALYSIS)

        # 多模态工具 (OpenRouter MCP)
        multimodal_keywords = ['chat_completion', 'vision', 'multimodal', 'image_url', 'visual', '视觉', '多模态', '图像分析']
        model_keywords = ['search_models', 'get_model_info', 'validate_model', 'model', '模型']
        if (any(keyword in tool_name.lower() or keyword in description.lower() for keyword in multimodal_keywords) or
            any(keyword in tool_name.lower() or keyword in description.lower() for keyword in model_keywords)):
            categories.append(ToolCategory.MULTIMODAL)

        return categories if categories else ['general']
    
    def _assess_tool_complexity(self, tool: Dict[str, Any]) -> str:
        """评估工具复杂度"""
        parameters = tool.get('parameters', {})
        param_count = len(parameters.get('properties', {}))
        required_params = len(parameters.get('required', []))
        
        if param_count <= 2 and required_params <= 1:
            return 'simple'
        elif param_count <= 5 and required_params <= 3:
            return 'medium'
        else:
            return 'complex'
    
    def recommend_tools_for_intent(self, 
                                 user_intent: str, 
                                 available_tools: List[Dict[str, Any]],
                                 workflow_context: Dict[str, Any] = None,
                                 max_recommendations: int = 10) -> List[Dict[str, Any]]:
        """基于用户意图推荐工具"""
        
        # 分析可用工具
        categorized_tools = self.analyze_available_tools(available_tools)
        
        # 解析用户意图
        intent_analysis = self._analyze_user_intent(user_intent)
        
        # 生成推荐
        recommendations = []
        
        for intent_category, confidence in intent_analysis.items():
            if confidence > 0.3:  # 置信度阈值
                category_tools = categorized_tools.get(intent_category, [])
                
                for tool_name in category_tools:
                    if tool_name in self.tool_capabilities:
                        tool_info = self.tool_capabilities[tool_name]
                        
                        # 计算推荐分数
                        score = self._calculate_recommendation_score(
                            tool_name, tool_info, user_intent, confidence, workflow_context
                        )
                        
                        recommendations.append({
                            'tool_name': tool_name,
                            'category': intent_category,
                            'score': score,
                            'confidence': confidence,
                            'complexity': tool_info['complexity'],
                            'reason': self._generate_recommendation_reason(tool_name, intent_category, confidence)
                        })
        
        # 排序并返回top推荐
        recommendations.sort(key=lambda x: x['score'], reverse=True)
        return recommendations[:max_recommendations]
    
    def _analyze_user_intent(self, user_intent: str) -> Dict[str, float]:
        """分析用户意图并返回各类别的置信度"""
        intent_lower = user_intent.lower()
        intent_scores = {}
        
        # 意图关键词映射和权重
        intent_patterns = {
            ToolCategory.TIMELINE: {
                'keywords': ['时间线', '剪辑', '序列', '轨道', 'timeline', 'edit', 'sequence', '分镜', 'motionboard'],
                'weight': 1.0
            },
            ToolCategory.IMAGE: {
                'keywords': ['图片', '图像', '静帧', '分镜', '草图', 'image', 'picture', 'frame', '图生图', 'storyboard'],
                'weight': 1.0
            },
            ToolCategory.VIDEO: {
                'keywords': ['视频', '片段', '生成视频', 'video', 'clip', 'generate', '动画'],
                'weight': 1.0
            },
            ToolCategory.AUDIO: {
                'keywords': ['音频', '语音', '配音', '字幕', 'audio', 'voice', 'speech', '音乐', 'music'],
                'weight': 1.0
            },
            ToolCategory.MEDIA: {
                'keywords': ['导入', '导出', '媒体', '素材', 'import', 'export', 'media', 'asset'],
                'weight': 0.8
            },
            ToolCategory.PROJECT: {
                'keywords': ['项目', '创建', '新建', 'project', 'create', 'new'],
                'weight': 0.7
            },
            ToolCategory.ANALYSIS: {
                'keywords': ['分析', '提取', '识别', 'analyze', 'extract', 'detect', '读取', 'parse'],
                'weight': 0.9
            },
            ToolCategory.MULTIMODAL: {
                'keywords': ['图片分析', '视觉分析', '图像识别', '看图', '分析图片', '描述图片', '图像理解',
                           '分析这张图片', '分析图', '图片', '照片', '图像', '画面', '视频帧',
                           'visual', 'vision', 'image analysis', 'multimodal', '多模态', '视觉',
                           '构图', '摄影', '色彩', '场景', '物体识别', '图像描述', '分析', '看'],
                'weight': 1.2  # 高权重，因为是新功能
            }
        }
        
        for category, pattern_info in intent_patterns.items():
            score = 0.0
            keywords = pattern_info['keywords']
            weight = pattern_info['weight']
            
            for keyword in keywords:
                if keyword in intent_lower:
                    # 基于关键词长度和出现次数计算分数
                    keyword_score = (len(keyword) / 10.0) * intent_lower.count(keyword)
                    score += keyword_score
            
            if score > 0:
                intent_scores[category] = min(score * weight, 1.0)  # 限制最大值为1.0
        
        return intent_scores
    
    def _calculate_recommendation_score(self, 
                                     tool_name: str, 
                                     tool_info: Dict[str, Any], 
                                     user_intent: str, 
                                     category_confidence: float,
                                     workflow_context: Dict[str, Any] = None) -> float:
        """计算工具推荐分数"""
        base_score = category_confidence
        
        # 工具名称匹配加分
        if any(word in tool_name.lower() for word in user_intent.lower().split()):
            base_score += 0.2
        
        # 描述匹配加分
        description = tool_info.get('description', '')
        intent_words = user_intent.lower().split()
        matching_words = sum(1 for word in intent_words if word in description)
        if matching_words > 0:
            base_score += (matching_words / len(intent_words)) * 0.3
        
        # 复杂度调整
        complexity = tool_info.get('complexity', 'medium')
        if complexity == 'simple':
            base_score += 0.1  # 简单工具优先
        elif complexity == 'complex':
            base_score -= 0.1  # 复杂工具降权
        
        # 使用历史加分
        if tool_name in self.usage_history:
            usage_count = self.usage_history[tool_name]
            base_score += min(usage_count * 0.05, 0.2)  # 最多加0.2分
        
        return min(base_score, 1.0)
    
    def _generate_recommendation_reason(self, tool_name: str, category: str, confidence: float) -> str:
        """生成推荐理由"""
        reasons = {
            ToolCategory.TIMELINE: f"适合时间线和剪辑操作 (置信度: {confidence:.2f})",
            ToolCategory.IMAGE: f"适合图像处理和生成 (置信度: {confidence:.2f})",
            ToolCategory.VIDEO: f"适合视频处理和生成 (置信度: {confidence:.2f})",
            ToolCategory.AUDIO: f"适合音频处理和合成 (置信度: {confidence:.2f})",
            ToolCategory.MEDIA: f"适合媒体管理操作 (置信度: {confidence:.2f})",
            ToolCategory.PROJECT: f"适合项目管理操作 (置信度: {confidence:.2f})",
            ToolCategory.ANALYSIS: f"适合分析和提取操作 (置信度: {confidence:.2f})"
        }
        
        return reasons.get(category, f"推荐工具 (置信度: {confidence:.2f})")
    
    def update_usage_history(self, tool_name: str):
        """更新工具使用历史"""
        if tool_name in self.usage_history:
            self.usage_history[tool_name] += 1
        else:
            self.usage_history[tool_name] = 1
    
    def get_tool_chain_recommendation(self, 
                                    user_intent: str, 
                                    available_tools: List[Dict[str, Any]]) -> List[List[str]]:
        """推荐工具链（多个工具的组合使用）"""
        
        # 常见的TVC工作流程工具链
        common_chains = [
            # Motionboard优化流程
            ['get_current_timeline', 'extract_frame', 'text_to_image', 'import_media', 'replace_clip'],
            # 脚本转视频流程
            ['parse_excel', 'text_to_image', 'generate_video', 'create_timeline', 'add_media_to_timeline'],
            # 音频制作流程
            ['extract_subtitles', 'text_to_audio', 'sync_audio', 'adjust_audio_levels'],
            # 完整制作流程
            ['create_project', 'text_to_image', 'generate_video', 'text_to_audio', 'auto_edit_sequence', 'export_timeline']
        ]
        
        # 过滤出可用的工具链
        available_tool_names = [tool.get('name', '') for tool in available_tools]
        valid_chains = []
        
        for chain in common_chains:
            valid_tools = [tool for tool in chain if tool in available_tool_names]
            if len(valid_tools) >= 3:  # 至少3个工具可用
                valid_chains.append(valid_tools)
        
        return valid_chains
