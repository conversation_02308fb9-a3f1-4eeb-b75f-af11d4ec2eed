"""
统一配置管理器
简化的配置加载和管理系统
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class ConfigManager:
    """统一配置管理器"""

    def __init__(self, config_path: Optional[str] = None):
        self.project_root = Path(__file__).parent.parent.parent
        self.config_path = config_path or self.project_root / 'config' / 'unified_config.json'
        self._config = None
        self._env_vars = {}
        self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            # 加载主配置文件
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info(f"配置文件加载成功: {self.config_path}")
            else:
                logger.warning(f"配置文件不存在: {self.config_path}")
                self._config = self._get_default_config()

            # 加载环境变量
            self._load_env_vars()

            # 解析环境变量占位符
            self._resolve_env_placeholders()

            return self._config

        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            self._config = self._get_default_config()
            return self._config

    def _load_env_vars(self):
        """加载环境变量"""
        env_file = self.project_root / '.env'
        if env_file.exists():
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            self._env_vars[key] = value
                            # 同时设置到os.environ中，确保子进程能够继承
                            os.environ[key] = value
                logger.info(f"环境变量加载成功: {len(self._env_vars)} 个变量")
            except Exception as e:
                logger.error(f"环境变量加载失败: {e}")

    def _resolve_env_placeholders(self):
        """解析环境变量占位符"""
        def resolve_value(value):
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                env_key = value[2:-1]
                return self._env_vars.get(env_key, os.getenv(env_key, value))
            elif isinstance(value, dict):
                return {k: resolve_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [resolve_value(item) for item in value]
            return value

        if self._config:
            self._config = resolve_value(self._config)

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值（支持点号分隔的嵌套键）"""
        if not self._config:
            return default

        keys = key.split('.')
        value = self._config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def get_provider_config(self, provider: str) -> Dict[str, Any]:
        """获取服务提供商配置"""
        return self.get(f'providers.{provider}', {})

    def get_mcp_config(self) -> Dict[str, Any]:
        """获取MCP配置"""
        return self.get('mcp', {})

    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.get('api', {})

    def is_provider_enabled(self, provider: str) -> bool:
        """检查服务提供商是否启用"""
        return self.get(f'providers.{provider}.enabled', False)

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "core": {
                "app_name": "DaVinci AI Co-pilot Pro",
                "version": "2.0.0",
                "debug": False
            },
            "providers": {},
            "mcp": {"enabled": True, "servers": {}},
            "api": {"host": "127.0.0.1", "port": 8000},
            "services": {}
        }

# 全局配置实例
config_manager = ConfigManager()

def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数 - 优先从环境变量获取，然后从配置文件获取"""
    # 首先尝试从环境变量获取
    env_value = config_manager._env_vars.get(key) or os.getenv(key)
    if env_value is not None:
        return env_value

    # 然后从配置文件获取
    return config_manager.get(key, default)

def validate_config() -> bool:
    """验证配置的有效性"""
    try:
        # 验证基本配置存在
        if not config_manager._config:
            logger.error("配置文件为空")
            return False

        # 验证核心配置
        core_config = get_config('core', {})
        if not core_config:
            logger.error("缺少核心配置")
            return False

        # 验证应用配置
        app_config = get_config('app', {})
        host = app_config.get('host', '127.0.0.1')
        port = app_config.get('port', 8000)

        if not isinstance(port, int) or port < 1 or port > 65535:
            logger.error(f"无效的端口配置: {port}")
            return False

        logger.info("配置验证通过")
        return True

    except Exception as e:
        logger.error(f"配置验证失败: {e}")
        return False
