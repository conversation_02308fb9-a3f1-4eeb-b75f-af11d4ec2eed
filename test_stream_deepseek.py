#!/usr/bin/env python3
"""
测试DeepSeek API流式端点修复
"""

import asyncio
import aiohttp
import json
import sys

async def test_stream_endpoint():
    """测试流式端点"""
    print("🧪 测试DeepSeek API流式端点修复")
    print("=" * 50)
    
    url = "http://127.0.0.1:8000/api/ai/process/stream"
    data = {"query": "请生成一张专业的TVC分镜图，内容是一个现代办公室场景"}
    
    print(f"📤 发送流式请求: {data['query']}")
    print(f"🌐 请求URL: {url}")
    print(f"📦 请求数据: {data}")
    print()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data) as response:
                print(f"📊 响应状态码: {response.status}")
                
                if response.status != 200:
                    error_text = await response.text()
                    print(f"❌ 请求失败: {error_text}")
                    return
                
                print("📡 开始接收流式数据:")
                print("-" * 30)
                
                tool_calls_detected = []
                tool_execution_results = []
                errors = []
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        if data_str == '[DONE]':
                            print("🏁 流式响应完成")
                            break
                        
                        try:
                            chunk_data = json.loads(data_str)
                            chunk_type = chunk_data.get('type', 'unknown')
                            
                            if chunk_type == 'status':
                                print(f"📊 状态: {chunk_data.get('message', '')}")
                            
                            elif chunk_type == 'content':
                                content = chunk_data.get('content', '')
                                if content:
                                    print(f"💬 内容: {content[:100]}...")
                            
                            elif chunk_type == 'tool_call_detected':
                                tool_name = chunk_data.get('tool_name', 'unknown')
                                print(f"🔧 检测到工具调用: {tool_name}")
                                tool_calls_detected.append(tool_name)
                            
                            elif chunk_type == 'tool_execution_start':
                                tool_name = chunk_data.get('tool_name', 'unknown')
                                arguments = chunk_data.get('arguments', {})
                                print(f"⚡ 开始执行工具: {tool_name}")
                                print(f"   参数: {arguments}")
                            
                            elif chunk_type == 'tool_execution_complete':
                                tool_name = chunk_data.get('tool_name', 'unknown')
                                result = chunk_data.get('result', {})
                                print(f"✅ 工具执行完成: {tool_name}")
                                print(f"   结果: {str(result)[:200]}...")
                                tool_execution_results.append((tool_name, result))
                            
                            elif chunk_type == 'tool_call_error':
                                tool_name = chunk_data.get('tool_name', 'unknown')
                                error = chunk_data.get('error', 'Unknown error')
                                print(f"❌ 工具调用错误: {tool_name}")
                                print(f"   错误: {error}")
                                errors.append((tool_name, error))
                            
                            elif chunk_type == 'error':
                                error = chunk_data.get('error', 'Unknown error')
                                print(f"❌ 系统错误: {error}")
                                errors.append(('system', error))
                            
                            elif chunk_type == 'complete':
                                print("🎉 处理完成")
                            
                            else:
                                print(f"📦 其他数据: {chunk_type} - {chunk_data}")
                        
                        except json.JSONDecodeError as e:
                            print(f"⚠️  JSON解析错误: {e}")
                            print(f"   原始数据: {data_str}")
                
                print("\n" + "=" * 50)
                print("📊 测试结果汇总:")
                print(f"🔧 检测到的工具调用: {tool_calls_detected}")
                print(f"✅ 成功执行的工具: {[name for name, _ in tool_execution_results]}")
                print(f"❌ 错误数量: {len(errors)}")
                
                if errors:
                    print("\n❌ 错误详情:")
                    for tool_name, error in errors:
                        print(f"   {tool_name}: {error}")
                
                # 判断测试结果
                if errors:
                    if any("空的工具参数" in error for _, error in errors):
                        print("\n🚨 DeepSeek API空参数问题仍然存在！")
                        return False
                    else:
                        print("\n⚠️  存在其他错误，但不是空参数问题")
                        return True
                else:
                    print("\n🎉 流式端点测试成功！没有发现空参数问题")
                    return True
    
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_stream_endpoint())
    sys.exit(0 if result else 1)
