#!/usr/bin/env python3
"""
测试流式端点的output_directory参数修复
"""

import asyncio
import aiohttp
import json
from pathlib import Path

async def test_streaming_endpoint():
    """测试流式端点是否正确处理output_directory参数"""
    print("🧪 测试流式端点的output_directory参数修复...")
    
    test_request = {
        "query": "请生成一张专业的TVC分镜图，内容是一个现代办公室场景",
        "context": {
            "workflow_type": "tvc_production",
            "user_preferences": {
                "style": "professional",
                "quality": "high"
            }
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:8000/api/ai/process/stream',
                json=test_request,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    print("✅ 流式端点连接成功")
                    
                    # 读取流式响应
                    progress_reached_100 = False
                    tool_calls_found = []
                    complete_event_received = False
                    
                    async for line in response.content:
                        if line:
                            try:
                                line_str = line.decode('utf-8').strip()
                                if line_str.startswith('data: '):
                                    data_str = line_str[6:]  # 移除 'data: ' 前缀
                                    if data_str == '[DONE]':
                                        print("✅ 收到流式结束标识")
                                        break
                                    
                                    data = json.loads(data_str)
                                    event_type = data.get('type')
                                    
                                    print(f"📡 收到事件: {event_type}")
                                    
                                    if event_type == 'progress':
                                        progress = data.get('progress', 0)
                                        step = data.get('step', '')
                                        print(f"   进度: {progress*100:.1f}% - {step}")
                                        if progress >= 1.0:
                                            progress_reached_100 = True
                                            print(f"✅ 进度达到100%!")
                                    
                                    elif event_type == 'tool_call':
                                        tool_name = data.get('tool_name')
                                        tool_args = data.get('arguments', {})
                                        tool_calls_found.append({
                                            'name': tool_name,
                                            'args': tool_args
                                        })
                                        
                                        print(f"🔧 工具调用: {tool_name}")
                                        
                                        # 检查output_directory参数
                                        if 'output_directory' in tool_args:
                                            output_dir = tool_args['output_directory']
                                            print(f"📁 output_directory: {output_dir}")
                                            
                                            # 验证是否是正确的用户路径
                                            expected_path = str(Path("~/Desktop/DaVinci_AI_Output").expanduser())
                                            if output_dir == expected_path:
                                                print("✅ 使用正确的用户自定义路径!")
                                            elif output_dir == "/Users/<USER>/Desktop":
                                                print("❌ 仍然使用错误的示例路径!")
                                            else:
                                                print(f"⚠️ 使用了其他路径: {output_dir}")
                                    
                                    elif event_type == 'complete':
                                        complete_event_received = True
                                        print("✅ 收到完成事件")
                                        break
                                        
                            except json.JSONDecodeError:
                                continue
                            except Exception as e:
                                print(f"⚠️ 处理流式数据时出错: {e}")
                    
                    # 总结测试结果
                    print(f"\n📊 测试结果总结:")
                    print(f"   - 进度是否达到100%: {'✅' if progress_reached_100 else '❌'}")
                    print(f"   - 是否收到完成事件: {'✅' if complete_event_received else '❌'}")
                    print(f"   - 工具调用数量: {len(tool_calls_found)}")
                    
                    for i, tool_call in enumerate(tool_calls_found):
                        print(f"   - 工具 {i+1}: {tool_call['name']}")
                        if 'output_directory' in tool_call['args']:
                            print(f"     output_directory: {tool_call['args']['output_directory']}")
                
                else:
                    print(f"❌ 流式端点请求失败: {response.status}")
                    error_text = await response.text()
                    print(f"错误信息: {error_text}")
                    
    except Exception as e:
        print(f"❌ 测试流式端点时出错: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("🚀 开始测试流式端点修复...")
    await test_streaming_endpoint()
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
