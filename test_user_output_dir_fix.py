#!/usr/bin/env python3
"""
测试用户自定义输出目录修复
验证：
1. 环境变量配置是否正确读取
2. PathManager是否正确处理用户自定义路径
3. DeepSeek API是否使用正确的输出目录
4. 前端进度条是否正确更新到100%
"""

import os
import sys
import asyncio
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置测试环境变量
os.environ['USER_OUTPUT_DIR'] = '~/Desktop/DaVinci_AI_Test_Output'
os.environ['LOG_LEVEL'] = 'DEBUG'

from dotenv import load_dotenv
load_dotenv(project_root / ".env")

async def test_path_manager():
    """测试PathManager的用户自定义路径功能"""
    print("🧪 测试 PathManager 用户自定义路径功能...")
    
    from src.core.path_manager import PathManager
    
    path_manager = PathManager()
    
    # 测试用户输出目录
    user_output = path_manager.get_output_directory()
    print(f"✅ 用户输出目录: {user_output}")
    
    # 测试项目输出目录
    project_output = path_manager.get_project_output_directory()
    print(f"✅ 项目输出目录: {project_output}")
    
    # 验证路径是否正确展开
    expected_user_path = Path("~/Desktop/DaVinci_AI_Test_Output").expanduser()
    actual_user_path = Path(user_output)
    
    if actual_user_path == expected_user_path:
        print("✅ 用户路径展开正确")
    else:
        print(f"❌ 用户路径展开错误: 期望 {expected_user_path}, 实际 {actual_user_path}")
    
    # 验证目录是否创建
    if actual_user_path.exists():
        print("✅ 用户输出目录已创建")
    else:
        print("❌ 用户输出目录未创建")
    
    return user_output

async def test_deepseek_tool_definition():
    """测试DeepSeek工具定义是否使用正确的输出目录"""
    print("\n🧪 测试 DeepSeek 工具定义...")
    
    try:
        from src.services.llm_client import DeepSeekLLMClient
        from src.services.mcp_client import MCPClient
        
        # 初始化MCP客户端
        mcp_client = MCPClient()
        await mcp_client.initialize()
        
        # 初始化DeepSeek客户端
        deepseek_client = DeepSeekLLMClient(mcp_client)
        
        # 获取工具定义
        tools = await deepseek_client._get_available_tools()
        
        # 查找text_to_image工具
        text_to_image_tool = None
        for tool in tools:
            if tool.get('function', {}).get('name') == 'text_to_image':
                text_to_image_tool = tool
                break
        
        if text_to_image_tool:
            print("✅ 找到 text_to_image 工具定义")
            
            # 检查output_directory参数
            parameters = text_to_image_tool.get('function', {}).get('parameters', {})
            properties = parameters.get('properties', {})
            
            if 'output_directory' in properties:
                output_dir_prop = properties['output_directory']
                default_value = output_dir_prop.get('default')
                
                print(f"📁 output_directory 默认值: {default_value}")
                
                # 验证是否使用了正确的用户自定义路径
                expected_path = Path("~/Desktop/DaVinci_AI_Test_Output").expanduser()
                if default_value and Path(default_value) == expected_path:
                    print("✅ output_directory 使用正确的用户自定义路径")
                else:
                    print(f"❌ output_directory 路径错误: 期望 {expected_path}, 实际 {default_value}")
            else:
                print("❌ 未找到 output_directory 参数")
        else:
            print("❌ 未找到 text_to_image 工具")
        
        await mcp_client.cleanup()
        
    except Exception as e:
        print(f"❌ 测试DeepSeek工具定义时出错: {e}")
        import traceback
        traceback.print_exc()

async def test_streaming_endpoint():
    """测试流式端点是否正确处理参数"""
    print("\n🧪 测试流式端点参数处理...")
    
    import aiohttp
    
    test_request = {
        "message": "请生成一张专业的TVC分镜图，内容是一个现代办公室场景",
        "context": {
            "workflow_type": "tvc_production",
            "user_preferences": {
                "style": "professional",
                "quality": "high"
            }
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:8080/api/ai/process/stream',
                json=test_request,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    print("✅ 流式端点连接成功")
                    
                    # 读取流式响应
                    progress_reached_100 = False
                    tool_calls_found = []
                    
                    async for line in response.content:
                        if line:
                            try:
                                line_str = line.decode('utf-8').strip()
                                if line_str.startswith('data: '):
                                    data_str = line_str[6:]  # 移除 'data: ' 前缀
                                    if data_str == '[DONE]':
                                        print("✅ 收到流式结束标识")
                                        break
                                    
                                    data = json.loads(data_str)
                                    event_type = data.get('type')
                                    
                                    if event_type == 'progress':
                                        progress = data.get('progress', 0)
                                        if progress >= 1.0:
                                            progress_reached_100 = True
                                            print(f"✅ 进度达到100%: {progress}")
                                    
                                    elif event_type == 'tool_call':
                                        tool_name = data.get('tool_name')
                                        tool_args = data.get('arguments', {})
                                        tool_calls_found.append({
                                            'name': tool_name,
                                            'args': tool_args
                                        })
                                        
                                        # 检查output_directory参数
                                        if 'output_directory' in tool_args:
                                            output_dir = tool_args['output_directory']
                                            print(f"📁 工具调用中的output_directory: {output_dir}")
                                            
                                            # 验证是否是正确的用户路径
                                            expected_path = str(Path("~/Desktop/DaVinci_AI_Test_Output").expanduser())
                                            if output_dir == expected_path:
                                                print("✅ 工具调用使用正确的用户自定义路径")
                                            else:
                                                print(f"❌ 工具调用路径错误: 期望 {expected_path}, 实际 {output_dir}")
                                    
                                    elif event_type == 'complete':
                                        print("✅ 收到完成事件")
                                        break
                                        
                            except json.JSONDecodeError:
                                continue
                            except Exception as e:
                                print(f"⚠️ 处理流式数据时出错: {e}")
                    
                    # 总结测试结果
                    print(f"\n📊 测试结果总结:")
                    print(f"   - 进度是否达到100%: {'✅' if progress_reached_100 else '❌'}")
                    print(f"   - 工具调用数量: {len(tool_calls_found)}")
                    
                    for i, tool_call in enumerate(tool_calls_found):
                        print(f"   - 工具 {i+1}: {tool_call['name']}")
                
                else:
                    print(f"❌ 流式端点请求失败: {response.status}")
                    
    except Exception as e:
        print(f"❌ 测试流式端点时出错: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始测试用户自定义输出目录修复...")
    
    # 测试1: PathManager
    user_output = await test_path_manager()
    
    # 测试2: DeepSeek工具定义
    await test_deepseek_tool_definition()
    
    # 测试3: 流式端点（需要服务器运行）
    print("\n⚠️ 请确保服务器正在运行 (python src/main.py)")
    input("按回车键继续测试流式端点...")
    await test_streaming_endpoint()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
