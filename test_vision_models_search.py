#!/usr/bin/env python3
"""
搜索OpenRouter中支持视觉功能的模型
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MCPClient

async def search_vision_models():
    """搜索支持视觉功能的模型"""
    print("👁️ OpenRouter 视觉模型搜索")
    print("=" * 50)
    
    # 初始化
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 搜索场景 - VL相关关键词搜索
    search_scenarios = [
        {
            "name": "🎯 关键词搜索: VL free",
            "params": {
                "query": "VL free"
            }
        },
        {
            "name": "🔍 关键词搜索: VL",
            "params": {
                "query": "VL"
            }
        },
        {
            "name": "🔍 关键词搜索: vl",
            "params": {
                "query": "vl"
            }
        },
        {
            "name": "🎯 关键词搜索: vision-language free",
            "params": {
                "query": "vision-language free"
            }
        },
        {
            "name": "🔍 关键词搜索: qwen vl free",
            "params": {
                "query": "qwen vl free"
            }
        }
    ]
    
    for i, scenario in enumerate(search_scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print("-" * 40)
        
        try:
            result = await mcp_client.call_tool(
                'search_models',
                scenario['params'],
                'openrouter'
            )
            
            if result and result.get('result'):
                content = result.get('result', [])
                if content and len(content) > 0:
                    try:
                        response_data = json.loads(content[0].get('text', '{}'))

                        if isinstance(response_data, list):
                            models = response_data
                        else:
                            models = response_data.get('data', response_data.get('models', []))

                        print(f"  ✅ 找到 {len(models)} 个模型")

                        # 显示前5个模型的详细信息
                        for j, model in enumerate(models[:5]):
                            if isinstance(model, dict):
                                model_id = model.get('id', 'Unknown')
                                name = model.get('name', model_id)
                                context_length = model.get('context_length', 'N/A')
                                pricing = model.get('pricing', {})
                                prompt_price = pricing.get('prompt', 'N/A')
                                completion_price = pricing.get('completion', 'N/A')

                                print(f"    {j+1}. {model_id}")
                                print(f"       名称: {name}")
                                print(f"       上下文: {context_length}")
                                print(f"       价格: ${prompt_price}/${completion_price}")

                        if len(models) > 5:
                            print(f"    ... 还有 {len(models) - 5} 个模型")

                    except json.JSONDecodeError as e:
                        print(f"  ⚠️  JSON解析错误: {e}")
                        print(f"  📄 原始响应: {content[0].get('text', '')[:200]}...")
            else:
                print(f"  ❌ 搜索失败")
                if result:
                    error_msg = result.get('result', [{}])[0].get('text', 'Unknown error')
                    print(f"  🚨 错误信息: {error_msg[:150]}...")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    print(f"\n🎉 视觉模型搜索完成!")

if __name__ == "__main__":
    asyncio.run(search_vision_models())
