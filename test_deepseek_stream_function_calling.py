#!/usr/bin/env python3
"""
直接测试DeepSeek API是否支持流式Function Calling
"""

import asyncio
import json
import os
from openai import AsyncOpenAI

async def test_deepseek_stream_function_calling():
    """测试DeepSeek API流式Function Calling"""
    print("🧪 测试DeepSeek API流式Function Calling支持")
    print("=" * 60)
    
    # 初始化客户端
    client = AsyncOpenAI(
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        base_url="https://api.deepseek.com/v1"
    )
    
    # 定义简单的工具
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get weather information for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city name"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    # 测试消息
    messages = [
        {"role": "user", "content": "What's the weather like in Beijing?"}
    ]
    
    print("📤 发送请求:")
    print(f"   消息: {messages[0]['content']}")
    print(f"   工具数量: {len(tools)}")
    print(f"   工具名称: {tools[0]['function']['name']}")
    print()
    
    try:
        print("🔄 测试流式模式...")
        print("-" * 30)
        
        # 流式请求
        stream = await client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            tools=tools,
            tool_choice="auto",
            stream=True,
            temperature=0.7
        )
        
        content_chunks = []
        tool_calls_detected = False
        tool_calls_data = {}
        
        async for chunk in stream:
            if chunk.choices and len(chunk.choices) > 0:
                choice = chunk.choices[0]
                delta = choice.delta
                
                print(f"📦 Chunk: {chunk}")
                
                # 检查内容
                if delta.content:
                    content_chunks.append(delta.content)
                    print(f"💬 内容: {delta.content}")
                
                # 检查工具调用
                if hasattr(delta, 'tool_calls') and delta.tool_calls:
                    tool_calls_detected = True
                    print(f"🔧 工具调用检测到: {delta.tool_calls}")
                    
                    for tool_call_delta in delta.tool_calls:
                        print(f"   - Index: {getattr(tool_call_delta, 'index', 'N/A')}")
                        print(f"   - ID: {getattr(tool_call_delta, 'id', 'N/A')}")
                        if hasattr(tool_call_delta, 'function'):
                            print(f"   - Function Name: {getattr(tool_call_delta.function, 'name', 'N/A')}")
                            print(f"   - Function Args: {getattr(tool_call_delta.function, 'arguments', 'N/A')}")
                
                # 检查完成原因
                if choice.finish_reason:
                    print(f"🏁 完成原因: {choice.finish_reason}")
                    break
        
        print("\n" + "=" * 60)
        print("📊 测试结果:")
        print(f"💬 内容chunks数量: {len(content_chunks)}")
        print(f"🔧 检测到工具调用: {tool_calls_detected}")
        
        if content_chunks:
            full_content = "".join(content_chunks)
            print(f"📝 完整内容: {full_content}")
        
        if not tool_calls_detected:
            print("❌ DeepSeek API流式模式下未检测到工具调用")
            print("   这可能表明DeepSeek API不支持流式Function Calling")
        else:
            print("✅ DeepSeek API流式模式下成功检测到工具调用")
        
        return tool_calls_detected
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

async def test_deepseek_non_stream_function_calling():
    """测试DeepSeek API非流式Function Calling作为对比"""
    print("\n🧪 测试DeepSeek API非流式Function Calling（对比）")
    print("=" * 60)
    
    # 初始化客户端
    client = AsyncOpenAI(
        api_key=os.getenv("DEEPSEEK_API_KEY"),
        base_url="https://api.deepseek.com/v1"
    )
    
    # 定义简单的工具
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get weather information for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "The city name"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    # 测试消息
    messages = [
        {"role": "user", "content": "What's the weather like in Beijing?"}
    ]
    
    try:
        print("🔄 测试非流式模式...")
        
        # 非流式请求
        response = await client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            tools=tools,
            tool_choice="auto",
            stream=False,
            temperature=0.7
        )
        
        print(f"📦 响应: {response}")
        
        message = response.choices[0].message
        print(f"💬 内容: {message.content}")
        
        if hasattr(message, 'tool_calls') and message.tool_calls:
            print(f"🔧 工具调用: {message.tool_calls}")
            for tool_call in message.tool_calls:
                print(f"   - ID: {tool_call.id}")
                print(f"   - Function: {tool_call.function.name}")
                print(f"   - Arguments: {tool_call.function.arguments}")
            return True
        else:
            print("❌ 非流式模式下也未检测到工具调用")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

async def main():
    """主函数"""
    # 检查API密钥
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("❌ 请设置DEEPSEEK_API_KEY环境变量")
        return
    
    # 测试流式模式
    stream_result = await test_deepseek_stream_function_calling()
    
    # 测试非流式模式作为对比
    non_stream_result = await test_deepseek_non_stream_function_calling()
    
    print("\n" + "=" * 60)
    print("🎯 最终结论:")
    print(f"   非流式Function Calling: {'✅ 支持' if non_stream_result else '❌ 不支持'}")
    print(f"   流式Function Calling: {'✅ 支持' if stream_result else '❌ 不支持'}")
    
    if non_stream_result and not stream_result:
        print("\n💡 结论: DeepSeek API支持非流式Function Calling，但不支持流式Function Calling")
        print("   建议: 在流式模式下禁用Function Calling，或者改用非流式模式处理工具调用")

if __name__ == "__main__":
    asyncio.run(main())
