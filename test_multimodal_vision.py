#!/usr/bin/env python3
"""
测试OpenRouter MCP多模态视觉能力
"""
import asyncio
import json
import sys
import base64
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MC<PERSON>lient

def encode_image_to_base64(image_path):
    """将图片编码为base64"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        print(f"图片编码失败: {e}")
        return None

async def test_multimodal_vision():
    """测试多模态视觉能力"""
    print("👁️ OpenRouter MCP 多模态视觉测试")
    print("=" * 50)
    
    # 初始化
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 创建一个简单的测试图片（如果没有现成的图片）
    test_image_url = "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg"
    
    # 测试场景
    test_scenarios = [
        {
            "name": "🎯 Qwen VL 72B - 网络图片分析",
            "params": {
                "model": "qwen/qwen2.5-vl-72b-instruct:free",
                "messages": [
                    {
                        "role": "user", 
                        "content": [
                            {
                                "type": "text",
                                "text": "请详细分析这张图片的内容，包括场景、物体、颜色、构图等方面。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": test_image_url
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 500
            }
        },
        {
            "name": "🎯 Qwen VL 32B - 网络图片分析",
            "params": {
                "model": "qwen/qwen2.5-vl-32b-instruct:free",
                "messages": [
                    {
                        "role": "user", 
                        "content": [
                            {
                                "type": "text",
                                "text": "这张图片展示了什么场景？请用中文回答。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": test_image_url
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 300
            }
        },
        {
            "name": "🤖 自动选择模型 - 图片描述",
            "params": {
                "model": "openrouter/auto",
                "messages": [
                    {
                        "role": "user", 
                        "content": [
                            {
                                "type": "text",
                                "text": "请简要描述这张图片。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": test_image_url
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 200
            }
        },
        {
            "name": "🎨 创意分析 - 摄影技巧",
            "params": {
                "model": "qwen/qwen2.5-vl-72b-instruct:free",
                "messages": [
                    {
                        "role": "user", 
                        "content": [
                            {
                                "type": "text",
                                "text": "从摄影师的角度分析这张照片的拍摄技巧、光线运用和构图特点。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": test_image_url
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 400
            }
        }
    ]
    
    print(f"📸 测试图片: {test_image_url}")
    print()
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"{i}. {scenario['name']}")
        print("-" * 40)
        
        try:
            result = await mcp_client.call_tool(
                'chat_completion',
                scenario['params'],
                'openrouter'
            )
            
            if result and result.get('result'):
                content = result.get('result', [])
                if content and len(content) > 0:
                    try:
                        response_data = json.loads(content[0].get('text', '{}'))
                        
                        # 提取关键信息
                        model_used = response_data.get('model', 'Unknown')
                        choices = response_data.get('choices', [])
                        usage = response_data.get('usage', {})
                        
                        print(f"  ✅ 多模态分析成功")
                        print(f"  🎯 使用模型: {model_used}")
                        print(f"  📊 Token使用: {usage.get('total_tokens', 'N/A')}")
                        print(f"  💰 成本: $0 (免费模型)")
                        
                        if choices:
                            message = choices[0].get('message', {}).get('content', '')
                            print(f"  🔍 视觉分析结果:")
                            print(f"     {message[:200]}...")
                            if len(message) > 200:
                                print(f"     [完整回答共{len(message)}字符]")
                        
                    except json.JSONDecodeError:
                        print(f"  ⚠️  响应格式异常")
                        print(f"  📄 原始响应: {content[0].get('text', '')[:100]}...")
            else:
                print(f"  ❌ 多模态分析失败")
                if result:
                    error_msg = result.get('result', [{}])[0].get('text', 'Unknown error')
                    print(f"  🚨 错误信息: {error_msg[:150]}...")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        print()
    
    print(f"🎉 多模态视觉测试完成!")
    print(f"📝 测试总结:")
    print(f"   - 使用了公开的自然风景图片进行测试")
    print(f"   - 测试了不同的VL模型和提示词")
    print(f"   - 验证了图片URL格式的多模态输入")

if __name__ == "__main__":
    asyncio.run(test_multimodal_vision())
