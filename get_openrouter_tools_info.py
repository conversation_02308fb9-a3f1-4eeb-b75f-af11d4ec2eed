#!/usr/bin/env python3
"""
获取OpenRouter MCP工具详细信息
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MCPClient

async def get_openrouter_tools_info():
    """获取OpenRouter MCP工具详细信息"""
    print("🔍 OpenRouter MCP工具信息获取")
    print("=" * 50)
    
    # 初始化
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 获取所有工具
    all_tools = mcp_client.get_available_tools()
    
    # 筛选OpenRouter工具
    openrouter_tools = [tool for tool in all_tools if tool.get('server_name') == 'openrouter']
    
    print(f"📊 发现 {len(openrouter_tools)} 个OpenRouter工具")
    print()
    
    # 详细分析每个工具
    for i, tool in enumerate(openrouter_tools, 1):
        print(f"{i}. 🔧 **{tool['name']}**")
        print(f"   📝 描述: {tool['description']}")
        print(f"   🏷️  服务器: {tool['server_name']}")
        
        # 分析参数结构
        schema = tool.get('input_schema', {})
        properties = schema.get('properties', {})
        required = schema.get('required', [])
        
        print(f"   📋 参数结构:")
        if properties:
            for param_name, param_info in properties.items():
                param_type = param_info.get('type', 'unknown')
                param_desc = param_info.get('description', '无描述')
                is_required = "✅ 必需" if param_name in required else "⚪ 可选"
                print(f"      - {param_name} ({param_type}) {is_required}")
                print(f"        {param_desc}")
        else:
            print(f"      无参数")
        
        print()
    
    # 生成prompt集成建议
    print("💡 **Prompt集成建议**")
    print("=" * 50)
    
    # 分析工具功能
    tool_functions = {
        'chat_completion': {
            'category': '多模态对话',
            'use_cases': ['图像分析', '视觉问答', '创意指导', '专业评价'],
            'prompt_guidance': '用于需要视觉理解和分析的任务，支持图片URL输入'
        },
        'search_models': {
            'category': '模型搜索',
            'use_cases': ['查找特定能力模型', '模型对比', '能力验证'],
            'prompt_guidance': '用于动态发现和选择最适合的AI模型'
        },
        'get_model_info': {
            'category': '模型信息',
            'use_cases': ['模型详情查询', '参数了解', '能力确认'],
            'prompt_guidance': '用于获取模型的详细技术信息和能力说明'
        },
        'validate_model': {
            'category': '模型验证',
            'use_cases': ['模型可用性检查', '兼容性验证'],
            'prompt_guidance': '用于确认模型ID的有效性和可用性'
        }
    }
    
    for tool in openrouter_tools:
        tool_name = tool['name']
        if tool_name in tool_functions:
            info = tool_functions[tool_name]
            print(f"🎯 **{tool_name}** ({info['category']})")
            print(f"   适用场景: {', '.join(info['use_cases'])}")
            print(f"   Prompt指导: {info['prompt_guidance']}")
            print()
    
    # 生成具体的prompt模板建议
    print("📝 **Prompt模板建议**")
    print("=" * 50)
    
    multimodal_prompt = """
🔍 **多模态视觉分析能力**

当用户需要分析图片、视频帧或视觉内容时，你可以使用OpenRouter MCP的多模态功能：

**工具**: chat_completion
**适用场景**:
- 📸 图片内容分析和描述
- 🎨 视觉构图和美学评价  
- 🎬 视频帧分析和场景理解
- 📊 图表数据解读
- 🔍 物体识别和场景检测

**调用方式**:
```json
{
  "model": "qwen/qwen2.5-vl-72b-instruct:free",
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "请分析这张图片的内容"},
        {"type": "image_url", "image_url": {"url": "图片URL"}}
      ]
    }
  ],
  "max_tokens": 500
}
```

**优势**:
- ✅ 完全免费的VL模型
- ✅ 支持中文输入输出
- ✅ 专业级视觉分析
- ✅ 无需额外配置
"""
    
    print(multimodal_prompt)
    
    print("🎉 OpenRouter工具信息获取完成!")

if __name__ == "__main__":
    asyncio.run(get_openrouter_tools_info())
