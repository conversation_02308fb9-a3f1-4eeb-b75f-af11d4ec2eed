#!/usr/bin/env python3
"""
简单测试DeepSeek修复
"""
import requests
import json

def test_deepseek_api():
    """测试DeepSeek API"""
    print("🧪 测试DeepSeek API修复")
    print("=" * 40)
    
    # 测试请求
    url = "http://127.0.0.1:8000/api/ai/process"

    test_data = {
        "query": "请生成一张专业的TVC分镜图，内容是一个现代办公室场景"
    }
    
    print(f"📤 发送请求: {test_data['query']}")
    
    try:
        print(f"🌐 请求URL: {url}")
        print(f"📦 请求数据: {json.dumps(test_data, ensure_ascii=False)}")
        response = requests.post(url, json=test_data, timeout=300)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功")
            print(f"📊 响应状态: {result.get('success', 'unknown')}")
            
            if result.get('success'):
                print(f"🎉 API调用成功")
                print(f"📝 响应: {result.get('response', '')[:100]}...")
                
                # 检查工具调用
                tool_calls = result.get('tool_calls', [])
                if tool_calls:
                    print(f"🔧 工具调用数量: {len(tool_calls)}")
                    for i, tool_call in enumerate(tool_calls, 1):
                        tool_name = tool_call.get('tool_name', 'unknown')
                        parameters = tool_call.get('parameters', {})
                        print(f"   {i}. {tool_name}: {len(parameters)} 个参数")
                        
                        if parameters:
                            print(f"      ✅ 参数非空: {list(parameters.keys())}")
                        else:
                            print(f"      ❌ 参数为空")
                else:
                    print(f"⚠️  未检测到工具调用")
            else:
                print(f"❌ API调用失败: {result.get('error', 'unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
    
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_deepseek_api()
