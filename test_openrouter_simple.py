#!/usr/bin/env python3
"""
简单的OpenRouter MCP连接测试
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.mcp_client import MCPClient

async def test_openrouter_connection():
    """测试OpenRouter MCP连接"""
    print("🎯 OpenRouter MCP 连接测试")
    print("=" * 50)
    
    try:
        # 初始化MCP客户端
        print("📡 正在初始化MCP客户端...")
        mcp_client = MCPClient()
        
        # 初始化连接
        await mcp_client.initialize()
        
        # 获取服务器状态
        print("🔍 检查服务器连接状态...")
        server_status = mcp_client.get_server_status()

        print(f"📊 发现 {len(server_status)} 个MCP服务器:")
        for server_name, status in server_status.items():
            tool_count = status.get('tool_count', 0)
            is_connected = status.get('connected', False)
            status_icon = "✅" if is_connected else "❌"
            print(f"  {status_icon} {server_name}: {tool_count} 个工具")

        # 获取所有可用工具
        print("\n🔧 获取可用工具...")
        available_tools = mcp_client.get_available_tools()
        print(f"  发现 {len(available_tools)} 个可用工具")
        
        # 检查OpenRouter特定工具
        openrouter_tools = [tool for tool in available_tools if 'openrouter' in tool.get('server', '').lower() or any(keyword in tool.get('name', '').lower() for keyword in ['chat', 'model', 'search', 'validate'])]
        if openrouter_tools:
            print(f"🎉 OpenRouter工具发现成功: {len(openrouter_tools)} 个工具")
            for tool in openrouter_tools:
                server = tool.get('server', 'Unknown')
                name = tool.get('name', 'Unknown')
                print(f"  - [{server}] {name}")
        else:
            print("⚠️  未发现OpenRouter特定工具")

        # 显示所有服务器的工具分布
        print(f"\n📋 工具分布详情:")
        server_tools = {}
        for tool in available_tools:
            server = tool.get('server', 'Unknown')
            if server not in server_tools:
                server_tools[server] = []
            server_tools[server].append(tool.get('name', 'Unknown'))

        for server, tools in server_tools.items():
            print(f"  📦 {server}: {len(tools)} 个工具")
            if server == 'openrouter':
                for tool_name in tools[:5]:  # 显示前5个工具
                    print(f"    - {tool_name}")
                if len(tools) > 5:
                    print(f"    ... 还有 {len(tools) - 5} 个工具")
        
        print("\n✅ 测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await test_openrouter_connection()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
