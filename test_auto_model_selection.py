#!/usr/bin/env python3
"""
测试OpenRouter自动模型选择功能
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MCPClient

async def test_auto_model_selection():
    """测试自动模型选择"""
    print("🤖 OpenRouter 自动模型选择测试")
    print("=" * 50)
    
    # 初始化
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 测试场景
    test_scenarios = [
        {
            "name": "🎯 自动选择最佳模型",
            "params": {
                "model": "openrouter/auto",
                "messages": [{"role": "user", "content": "分析这个视频脚本的情感色调"}],
                "max_tokens": 500
            }
        },
        {
            "name": "💰 价格优先选择",
            "params": {
                "messages": [{"role": "user", "content": "为TVC广告生成创意概念"}],
                "max_tokens": 300,
                "provider": {
                    "sort": "price",
                    "allow_fallbacks": True
                }
            }
        },
        {
            "name": "🚀 性能优先选择",
            "params": {
                "messages": [{"role": "user", "content": "快速生成视频标题建议"}],
                "max_tokens": 200,
                "provider": {
                    "sort": "throughput",
                    "quantizations": ["fp16"],
                    "allow_fallbacks": True
                }
            }
        },
        {
            "name": "👁️ 视觉模型优先",
            "params": {
                "messages": [{"role": "user", "content": "分析这个画面构图"}],
                "max_tokens": 400,
                "provider": {
                    "order": ["qwen/qwen2.5-vl-32b-instruct:free", "google/gemini-pro-vision"],
                    "require_parameters": True,
                    "allow_fallbacks": True
                }
            }
        },
        {
            "name": "🔒 严格参数要求",
            "params": {
                "messages": [{"role": "user", "content": "生成结构化的视频制作计划"}],
                "max_tokens": 600,
                "temperature": 0.7,
                "provider": {
                    "require_parameters": True,
                    "data_collection": "deny",
                    "allow_fallbacks": False
                }
            }
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print("-" * 40)
        
        try:
            result = await mcp_client.call_tool(
                'chat_completion',
                scenario['params'],
                'openrouter'
            )
            
            if result and not result.get('isError', True):
                content = result.get('result', [])
                if content and len(content) > 0:
                    try:
                        response_data = json.loads(content[0].get('text', '{}'))
                        
                        # 提取关键信息
                        model_used = response_data.get('model', 'Unknown')
                        choices = response_data.get('choices', [])
                        usage = response_data.get('usage', {})
                        
                        print(f"  ✅ 成功调用")
                        print(f"  🎯 使用模型: {model_used}")
                        print(f"  📊 Token使用: {usage.get('total_tokens', 'N/A')}")
                        
                        if choices:
                            message = choices[0].get('message', {}).get('content', '')
                            print(f"  💬 响应预览: {message[:80]}...")
                        
                    except json.JSONDecodeError:
                        print(f"  ⚠️  响应格式异常")
                        print(f"  📄 原始响应: {content[0].get('text', '')[:100]}...")
            else:
                print(f"  ❌ 调用失败")
                if result:
                    error_msg = result.get('result', [{}])[0].get('text', 'Unknown error')
                    print(f"  🚨 错误信息: {error_msg[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    print(f"\n🎉 自动模型选择测试完成!")

if __name__ == "__main__":
    asyncio.run(test_auto_model_selection())
