{"_metadata": {"version": "3.0.0", "created": "2025-07-28", "description": "DaVinci AI Co-pilot Pro Cherry Studio风格简化配置", "architecture": "MCP-First Auto-Discovery", "migration_from": "unified_config.json v2.0.0"}, "core": {"app_name": "DaVinci AI Co-pilot <PERSON>", "version": "0.2.0", "debug": true, "log_level": "INFO", "host": "127.0.0.1", "port": 8000}, "mcp": {"enabled": true, "timeout": 30, "auto_discovery": {"enabled": true, "cache_ttl": 300, "retry_count": 3, "health_check_interval": 300}, "servers": {"minimax": {"command": "uvx", "args": ["minimax-mcp"], "env": {"MINIMAX_API_KEY": "${MINIMAX_API_KEY}", "MINIMAX_GROUP_ID": "${MINIMAX_GROUP_ID}", "MINIMAX_API_HOST": "${MINIMAX_API_HOST}", "MINIMAX_MCP_BASE_PATH": "${USER_OUTPUT_DIR}", "MINIMAX_API_RESOURCE_MODE": "local"}}, "davinci-resolve": {"command": "/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/external/davinci-resolve-mcp/venv/bin/python", "args": ["/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/external/davinci-resolve-mcp/src/resolve_mcp_server.py"], "env": {"RESOLVE_SCRIPT_API": "/Library/Application Support/Blackmagic Design/DaVinci Resolve/Developer/Scripting", "RESOLVE_SCRIPT_LIB": "/Applications/DaVinci Resolve/DaVinci Resolve.app/Contents/Libraries/Fusion/fusionscript.so", "PYTHONPATH": "/Library/Application Support/Blackmagic Design/DaVinci Resolve/Developer/Scripting/Modules/:/Library/Application Support/Blackmagic Design/DaVinci Resolve/Fusion/Scripts/Utility/DaVinci AI Co-pilot Pro/external/davinci-resolve-mcp"}}, "openrouter": {"command": "npx", "args": ["-y", "@mcpservers/openrouterai"], "env": {"OPENROUTER_API_KEY": "${OPENROUTER_API_KEY}", "OPENROUTER_DEFAULT_MODEL": "openrouter/auto", "OPENROUTER_MAX_TOKENS": "4000", "OPENROUTER_PROVIDER_SORT": "price", "OPENROUTER_PROVIDER_ORDER": "[\"moonshotai/kimi-k2:free，“google/gemini-2.0-flash-exp:free\", \"qwen/qwen2.5-vl-72b-instruct:free\", \"qwen/qwen2.5-vl-32b-instruct:free\", \"deepseek/deepseek-r1-0528:free\"]", "OPENROUTER_PROVIDER_DATA_COLLECTION": "deny", "OPENROUTER_PROVIDER_ALLOW_FALLBACKS": "true"}}}}, "api": {"host": "127.0.0.1", "port": 8000, "cors_origins": ["*"], "rate_limit": {"enabled": true, "requests_per_minute": 60}}, "services": {"direct_adapter": {"enabled": true, "fallback_to_mcp": true}}}