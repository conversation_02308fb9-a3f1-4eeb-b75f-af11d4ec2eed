#!/usr/bin/env python3
"""
调试MCP工具Schema问题
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import Config<PERSON>anager
from src.services.mcp_client import MCPClient

async def debug_mcp_tool_schema():
    """调试MCP工具Schema"""
    print("🔍 MCP工具Schema调试")
    print("=" * 50)
    
    # 初始化MCP客户端
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 获取原始工具信息
    print("📊 服务器状态:")
    server_status = mcp_client.get_server_status()
    for server_name, status in server_status.items():
        print(f"   - {server_name}: {status['tools_count']} 工具, 连接: {status['connected']}")
    
    # 检查问题工具的原始数据
    problem_tools = ['text_to_image', 'text_to_audio', 'chat_completion']
    
    for tool_name in problem_tools:
        print(f"\n🔍 调试工具: {tool_name}")
        print("-" * 40)
        
        # 获取工具信息
        tool_info = mcp_client.get_tool_info(tool_name)
        if not tool_info:
            print(f"   ❌ 未找到工具信息")
            continue
        
        print(f"   ✅ 找到工具信息:")
        print(f"      名称: {tool_info.name}")
        print(f"      服务器: {tool_info.server_name}")
        print(f"      描述: {tool_info.description[:100]}...")
        
        # 检查input_schema
        print(f"      input_schema类型: {type(tool_info.input_schema)}")
        print(f"      input_schema值: {tool_info.input_schema}")
        
        if tool_info.input_schema is None:
            print(f"   ❌ input_schema为None")
        elif not tool_info.input_schema:
            print(f"   ❌ input_schema为空")
        else:
            print(f"   ✅ input_schema存在")
            try:
                schema_str = json.dumps(tool_info.input_schema, indent=2, ensure_ascii=False)
                print(f"   Schema内容:\n{schema_str}")
            except Exception as e:
                print(f"   ❌ Schema序列化失败: {e}")
    
    # 检查服务器会话中的原始工具数据
    print(f"\n🔍 检查服务器会话原始数据")
    print("-" * 40)
    
    for server_name, session_info in mcp_client.server_sessions.items():
        if not session_info.connected:
            continue
            
        print(f"\n服务器: {server_name}")
        print(f"工具数量: {len(session_info.tools)}")
        
        for tool_name in problem_tools:
            if tool_name in session_info.tools:
                raw_tool = session_info.tools[tool_name]
                print(f"   {tool_name}:")
                print(f"      原始input_schema: {raw_tool.input_schema}")
                print(f"      类型: {type(raw_tool.input_schema)}")
    
    # 尝试直接从MCP服务器获取工具列表
    print(f"\n🔍 直接从MCP服务器获取工具")
    print("-" * 40)
    
    for server_name, session_info in mcp_client.server_sessions.items():
        if not session_info.connected or not session_info.session:
            continue
            
        print(f"\n服务器: {server_name}")
        try:
            # 直接调用list_tools
            from mcp.client.session import ClientSession
            from mcp.types import ListToolsRequest
            
            session = session_info.session
            if isinstance(session, ClientSession):
                request = ListToolsRequest()
                response = await session.list_tools(request)
                
                print(f"   直接获取到 {len(response.tools)} 个工具")
                
                for tool in response.tools:
                    if tool.name in problem_tools:
                        print(f"   {tool.name}:")
                        print(f"      描述: {tool.description[:50]}...")
                        print(f"      inputSchema: {tool.inputSchema}")
                        print(f"      inputSchema类型: {type(tool.inputSchema)}")
                        
                        if hasattr(tool, 'inputSchema') and tool.inputSchema:
                            try:
                                schema_dict = tool.inputSchema
                                if hasattr(schema_dict, 'model_dump'):
                                    schema_dict = schema_dict.model_dump()
                                elif hasattr(schema_dict, '__dict__'):
                                    schema_dict = schema_dict.__dict__
                                
                                print(f"      Schema字典: {schema_dict}")
                            except Exception as e:
                                print(f"      Schema转换失败: {e}")
        
        except Exception as e:
            print(f"   ❌ 直接获取失败: {e}")
    
    # 总结问题
    print(f"\n📊 **问题总结**")
    print("=" * 50)
    print("可能的原因：")
    print("1. MCP服务器返回的工具没有inputSchema")
    print("2. inputSchema在传输过程中丢失")
    print("3. inputSchema格式不正确")
    print("4. MCP客户端解析问题")
    
    print(f"\n🔧 **修复方向**:")
    print("1. 检查MCP服务器的工具定义")
    print("2. 修复inputSchema解析逻辑")
    print("3. 为缺失的Schema添加默认值")
    print("4. 更新工具转换逻辑")

if __name__ == "__main__":
    asyncio.run(debug_mcp_tool_schema())
