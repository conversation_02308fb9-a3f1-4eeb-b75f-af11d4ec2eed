#!/usr/bin/env python3
"""
直接测试DeepSeek API后端工具调用
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MC<PERSON><PERSON>
from src.services.llm_client import DeepSeekLLMClient

async def test_deepseek_backend_direct():
    """直接测试DeepSeek后端工具调用"""
    print("🔧 DeepSeek后端直接测试")
    print("=" * 50)
    
    # 初始化组件
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 获取可用工具
    available_tools = mcp_client.get_available_tools()
    print(f"📊 发现 {len(available_tools)} 个可用工具")
    
    # 筛选测试工具
    test_tools = []
    for tool in available_tools:
        tool_name = tool.get('name', '')
        if tool_name in ['text_to_image', 'text_to_audio', 'chat_completion']:
            test_tools.append(tool)
            print(f"   - {tool_name} ({tool.get('server_name', 'unknown')})")
    
    print(f"\n🎯 将测试 {len(test_tools)} 个工具")
    
    # 初始化DeepSeek客户端
    import os
    deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
    if not deepseek_api_key:
        print("❌ DEEPSEEK_API_KEY环境变量未设置")
        return

    deepseek_client = DeepSeekLLMClient(deepseek_api_key, mcp_client)
    
    # 测试场景
    test_scenarios = [
        {
            "name": "text_to_image测试",
            "query": "请生成一张专业的TVC分镜图",
            "expected_tool": "text_to_image"
        },
        {
            "name": "text_to_audio测试", 
            "query": "请为这段文字生成专业的配音：欢迎来到我们的产品世界",
            "expected_tool": "text_to_audio"
        },
        {
            "name": "chat_completion测试",
            "query": "请分析这张图片的构图效果",
            "expected_tool": "chat_completion"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print("-" * 40)
        print(f"   查询: {scenario['query']}")
        
        try:
            # 直接调用DeepSeek API
            print(f"   🔄 调用DeepSeek API...")
            
            # 获取工具定义
            tool_definitions = deepseek_client._get_available_tools(available_tools)
            
            # 检查目标工具是否在定义中
            target_tool_def = None
            for tool_def in tool_definitions:
                if tool_def['function']['name'] == scenario['expected_tool']:
                    target_tool_def = tool_def
                    break
            
            if not target_tool_def:
                print(f"   ❌ 未找到工具定义: {scenario['expected_tool']}")
                continue
            
            print(f"   ✅ 找到工具定义: {scenario['expected_tool']}")
            print(f"   📋 工具定义预览:")
            print(f"      名称: {target_tool_def['function']['name']}")
            print(f"      描述: {target_tool_def['function']['description'][:100]}...")
            
            # 检查参数结构
            parameters = target_tool_def['function'].get('parameters', {})
            properties = parameters.get('properties', {})
            required = parameters.get('required', [])
            
            print(f"      参数数量: {len(properties)}")
            print(f"      必需参数: {required}")
            
            if not properties:
                print(f"   ❌ 工具定义缺少参数结构")
                continue
            
            if not required:
                print(f"   ⚠️  工具定义缺少required字段")
            
            # 构建测试消息
            messages = [
                {
                    "role": "system",
                    "content": f"""你是专业的AI助手。用户请求需要使用{scenario['expected_tool']}工具。

工具调用示例：
- text_to_image: {{"prompt": "professional advertising image, high quality"}}
- text_to_audio: {{"text": "要转换的文字内容", "voice_id": "professional"}}
- chat_completion: {{"model": "qwen/qwen2.5-vl-72b-instruct:free", "messages": [{{"role": "user", "content": "分析内容"}}]}}

请根据用户请求生成正确的工具调用参数。"""
                },
                {
                    "role": "user", 
                    "content": scenario['query']
                }
            ]
            
            # 调用DeepSeek API
            response = await deepseek_client.client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                tools=[target_tool_def],
                tool_choice="auto",
                temperature=0.1
            )
            
            print(f"   📤 API响应状态: 成功")
            
            # 检查响应
            if response.choices and response.choices[0].message.tool_calls:
                tool_call = response.choices[0].message.tool_calls[0]
                print(f"   ✅ 工具调用成功:")
                print(f"      工具名: {tool_call.function.name}")
                print(f"      参数: {tool_call.function.arguments}")
                
                # 验证参数
                try:
                    params = json.loads(tool_call.function.arguments)
                    if params:
                        print(f"   ✅ 参数解析成功，包含 {len(params)} 个字段")
                        for key, value in params.items():
                            print(f"      - {key}: {str(value)[:50]}...")
                    else:
                        print(f"   ❌ 参数为空: {params}")
                except json.JSONDecodeError as e:
                    print(f"   ❌ 参数JSON解析失败: {e}")
                    print(f"   原始参数: {tool_call.function.arguments}")
            else:
                print(f"   ❌ 未返回工具调用")
                if response.choices:
                    print(f"   响应内容: {response.choices[0].message.content}")
        
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 总结测试
    print(f"\n📊 **测试总结**")
    print("=" * 50)
    print("如果所有测试都失败，可能的原因：")
    print("1. 工具定义格式问题（缺少required字段）")
    print("2. DeepSeek API模型理解问题")
    print("3. 系统prompt不够明确")
    print("4. 工具参数结构复杂度过高")
    
    print(f"\n🔧 建议检查：")
    print("- 工具定义的JSON Schema格式")
    print("- required字段是否正确设置")
    print("- 参数描述是否清晰")
    print("- 系统prompt是否包含足够的示例")

if __name__ == "__main__":
    asyncio.run(test_deepseek_backend_direct())
