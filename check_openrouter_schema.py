#!/usr/bin/env python3
"""
检查OpenRouter工具的参数schema
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MCPClient

async def check_openrouter_schema():
    """检查OpenRouter工具schema"""
    print("🔍 OpenRouter 工具Schema检查")
    print("=" * 50)
    
    # 初始化
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 获取工具列表
    tools = mcp_client.get_available_tools()
    openrouter_tools = [tool for tool in tools if tool.get('server_name') == 'openrouter']
    
    print(f"发现 {len(openrouter_tools)} 个OpenRouter工具:")
    print()
    
    for tool in openrouter_tools:
        print(f"🔧 工具: {tool['name']}")
        print(f"📝 描述: {tool['description']}")
        print(f"📋 参数Schema:")
        schema = tool.get('input_schema', {})
        print(json.dumps(schema, indent=2, ensure_ascii=False))
        print("-" * 40)

if __name__ == "__main__":
    asyncio.run(check_openrouter_schema())
