#!/usr/bin/env python3
"""
测试OpenRouter VL免费模型配置
"""
import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MCPClient

async def test_vl_free_models():
    """测试VL免费模型配置"""
    print("🎯 OpenRouter VL免费模型测试")
    print("=" * 50)
    
    # 初始化
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 测试场景
    test_scenarios = [
        {
            "name": "🤖 自动选择最便宜模型",
            "params": {
                "model": "openrouter/auto",
                "messages": [{"role": "user", "content": "你好，请介绍一下你的视觉能力"}],
                "max_tokens": 200
            }
        },
        {
            "name": "🎯 指定Qwen VL 72B免费版",
            "params": {
                "model": "qwen/qwen2.5-vl-72b-instruct:free",
                "messages": [{"role": "user", "content": "分析这个图像的内容"}],
                "max_tokens": 300
            }
        },
        {
            "name": "🎯 指定Gemini 2.0 Flash免费版",
            "params": {
                "model": "google/gemini-2.0-flash-exp:free",
                "messages": [{"role": "user", "content": "描述这个视频场景"}],
                "max_tokens": 250
            }
        },
        {
            "name": "🎯 指定Kimi VL免费版",
            "params": {
                "model": "moonshotai/kimi-vl-a3b-thinking:free",
                "messages": [{"role": "user", "content": "解读这个图表数据"}],
                "max_tokens": 400
            }
        },
        {
            "name": "🎯 指定Qwen VL 32B免费版",
            "params": {
                "model": "qwen/qwen2.5-vl-32b-instruct:free",
                "messages": [{"role": "user", "content": "识别这个文档中的文字"}],
                "max_tokens": 350
            }
        },
        {
            "name": "🎯 指定DeepSeek R1免费版",
            "params": {
                "model": "deepseek/deepseek-r1-0528:free",
                "messages": [{"role": "user", "content": "推理这个问题的解决方案"}],
                "max_tokens": 500
            }
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print("-" * 40)
        
        try:
            result = await mcp_client.call_tool(
                'chat_completion',
                scenario['params'],
                'openrouter'
            )
            
            if result and result.get('result'):
                content = result.get('result', [])
                if content and len(content) > 0:
                    try:
                        response_data = json.loads(content[0].get('text', '{}'))
                        
                        # 提取关键信息
                        model_used = response_data.get('model', 'Unknown')
                        choices = response_data.get('choices', [])
                        usage = response_data.get('usage', {})
                        
                        print(f"  ✅ 成功调用")
                        print(f"  🎯 实际使用模型: {model_used}")
                        print(f"  📊 Token使用: {usage.get('total_tokens', 'N/A')}")
                        print(f"  💰 成本: $0 (免费模型)")
                        
                        if choices:
                            message = choices[0].get('message', {}).get('content', '')
                            print(f"  💬 响应预览: {message[:100]}...")
                        
                    except json.JSONDecodeError:
                        print(f"  ⚠️  响应格式异常")
                        print(f"  📄 原始响应: {content[0].get('text', '')[:100]}...")
            else:
                print(f"  ❌ 调用失败")
                if result:
                    error_msg = result.get('result', [{}])[0].get('text', 'Unknown error')
                    print(f"  🚨 错误信息: {error_msg[:100]}...")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    # 验证模型信息
    print(f"\n🔍 验证配置的模型")
    print("-" * 40)
    
    models_to_validate = [
        "qwen/qwen2.5-vl-72b-instruct:free",
        "google/gemini-2.0-flash-exp:free", 
        "moonshotai/kimi-vl-a3b-thinking:free",
        "qwen/qwen2.5-vl-32b-instruct:free",
        "deepseek/deepseek-r1-0528:free"
    ]
    
    for model in models_to_validate:
        try:
            result = await mcp_client.call_tool(
                'validate_model',
                {"model": model},
                'openrouter'
            )
            
            if result and result.get('result'):
                content = result.get('result', [])
                if content:
                    response_data = json.loads(content[0].get('text', '{}'))
                    is_valid = response_data.get('valid', False)
                    status = "✅ 有效" if is_valid else "❌ 无效"
                    print(f"  {status}: {model}")
            else:
                print(f"  ❓ 未知: {model}")
                
        except Exception as e:
            print(f"  ❌ 验证失败: {model} - {e}")
    
    print(f"\n🎉 VL免费模型测试完成!")

if __name__ == "__main__":
    asyncio.run(test_vl_free_models())
