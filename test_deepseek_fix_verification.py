#!/usr/bin/env python3
"""
验证DeepSeek修复是否生效
"""
import asyncio
import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager
from src.services.mcp_client import MC<PERSON>lient
from src.services.llm_client import DeepSeekLLMClient

async def test_deepseek_fix():
    """验证DeepSeek修复"""
    print("🔧 DeepSeek修复验证测试")
    print("=" * 50)
    
    # 检查环境变量
    deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
    if not deepseek_api_key:
        print("❌ DEEPSEEK_API_KEY环境变量未设置")
        return
    
    # 初始化组件
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    deepseek_client = DeepSeekLLMClient(deepseek_api_key, mcp_client)
    
    # 测试工具转换
    print("🔍 测试工具转换逻辑")
    print("-" * 30)
    
    try:
        # 获取转换后的工具定义
        available_tools = await deepseek_client._get_available_tools()
        
        print(f"✅ 成功获取 {len(available_tools)} 个工具")
        
        # 检查问题工具
        problem_tools = ['text_to_image', 'text_to_audio']
        
        for tool_name in problem_tools:
            print(f"\n🔍 检查工具: {tool_name}")
            print("-" * 25)
            
            # 查找工具定义
            tool_def = None
            for tool in available_tools:
                if tool['function']['name'] == tool_name:
                    tool_def = tool
                    break
            
            if not tool_def:
                print(f"   ❌ 未找到工具定义")
                continue
            
            print(f"   ✅ 找到工具定义")
            
            # 检查参数结构
            parameters = tool_def['function'].get('parameters', {})
            properties = parameters.get('properties', {})
            required = parameters.get('required', [])
            
            print(f"   参数数量: {len(properties)}")
            print(f"   必需参数: {required}")
            
            # 检查关键参数
            if tool_name == 'text_to_image':
                if 'prompt' in properties:
                    prompt_def = properties['prompt']
                    has_empty_default = prompt_def.get('default') == ""
                    is_required = 'prompt' in required
                    
                    print(f"   prompt参数:")
                    print(f"      有空默认值: {'❌ 是' if has_empty_default else '✅ 否'}")
                    print(f"      是必需参数: {'✅ 是' if is_required else '❌ 否'}")
                    
                    if has_empty_default:
                        print(f"   ⚠️  prompt仍有空默认值，修复未生效")
                    elif is_required:
                        print(f"   ✅ prompt修复成功")
                    else:
                        print(f"   ⚠️  prompt不是必需参数")
            
            elif tool_name == 'text_to_audio':
                if 'text' in properties:
                    text_def = properties['text']
                    has_default = 'default' in text_def
                    is_required = 'text' in required
                    
                    print(f"   text参数:")
                    print(f"      有默认值: {'是' if has_default else '否'}")
                    print(f"      是必需参数: {'✅ 是' if is_required else '❌ 否'}")
                    
                    if is_required:
                        print(f"   ✅ text参数正确")
                    else:
                        print(f"   ❌ text不是必需参数")
        
        # 测试实际API调用
        print(f"\n🚀 测试实际API调用")
        print("-" * 30)
        
        test_queries = [
            {
                "query": "请生成一张专业的TVC分镜图",
                "expected_tool": "text_to_image"
            },
            {
                "query": "请为这段文字生成配音：欢迎来到我们的产品世界",
                "expected_tool": "text_to_audio"
            }
        ]
        
        for i, test in enumerate(test_queries, 1):
            print(f"\n{i}. 测试: {test['query'][:30]}...")
            
            try:
                # 使用流式API测试
                result_generator = deepseek_client.process_natural_language_stream(test['query'])
                
                tool_calls_found = []
                async for result in result_generator:
                    if result.get('type') == 'tool_execution_start':
                        tool_name = result.get('tool_name')
                        tool_params = result.get('parameters', {})
                        
                        print(f"   🔧 工具调用: {tool_name}")
                        print(f"   📋 参数: {tool_params}")
                        
                        if tool_params:
                            print(f"   ✅ 参数生成成功，包含 {len(tool_params)} 个字段")
                            tool_calls_found.append(tool_name)
                        else:
                            print(f"   ❌ 参数为空")
                        
                        break  # 只检查第一个工具调用
                
                if not tool_calls_found:
                    print(f"   ❌ 未检测到工具调用")
                elif test['expected_tool'] in tool_calls_found:
                    print(f"   ✅ 成功调用期望的工具: {test['expected_tool']}")
                else:
                    print(f"   ⚠️  调用了其他工具: {tool_calls_found}")
            
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
    
    except Exception as e:
        print(f"❌ 工具转换失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 总结
    print(f"\n📊 **修复验证总结**")
    print("=" * 50)
    print("如果看到以下结果，说明修复成功：")
    print("1. ✅ text_to_image的prompt参数无空默认值且为必需")
    print("2. ✅ text_to_audio的text参数为必需")
    print("3. ✅ API调用能生成非空参数")
    
    print(f"\n如果仍有问题，可能需要：")
    print("1. 检查工具转换逻辑的条件判断")
    print("2. 确认DeepSeek API的工具定义格式")
    print("3. 验证系统prompt的工具调用示例")

if __name__ == "__main__":
    asyncio.run(test_deepseek_fix())
