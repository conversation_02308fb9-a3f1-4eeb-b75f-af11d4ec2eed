#!/usr/bin/env python3
"""
测试OpenRouter工具检测和分类
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.intelligent_tool_recommender import IntelligentToolRecommender, ToolCategory
from src.core.unified_config import ConfigManager
from src.services.mcp_client import MCPClient

async def test_openrouter_tool_detection():
    """测试OpenRouter工具检测"""
    print("🔍 OpenRouter工具检测测试")
    print("=" * 50)
    
    # 初始化
    config_manager = ConfigManager()
    config_manager.load_config()
    
    mcp_client = MCPClient()
    await mcp_client.initialize()
    
    # 获取所有工具
    all_tools = mcp_client.get_available_tools()
    
    # 筛选OpenRouter工具
    openrouter_tools = [tool for tool in all_tools if tool.get('server_name') == 'openrouter']
    
    print(f"📊 发现 {len(openrouter_tools)} 个OpenRouter工具")
    
    # 初始化工具推荐器
    tool_recommender = IntelligentToolRecommender()
    
    # 测试工具分类
    print(f"\n🏷️ **工具分类测试**")
    print("-" * 30)
    
    for tool in openrouter_tools:
        tool_name = tool['name']
        description = tool['description']
        
        # 测试分类
        categories = tool_recommender._categorize_tool(tool_name, description)
        
        print(f"🔧 {tool_name}")
        print(f"   描述: {description[:100]}...")
        print(f"   分类: {categories}")
        
        # 检查是否正确识别为多模态
        if ToolCategory.MULTIMODAL in categories:
            print(f"   ✅ 正确识别为多模态工具")
        else:
            print(f"   ❌ 未识别为多模态工具")
        print()
    
    # 测试意图分析
    print(f"🧠 **意图分析测试**")
    print("-" * 30)
    
    test_intents = [
        "分析这张图片",
        "图像识别",
        "视觉分析",
        "看图说话",
        "multimodal analysis",
        "vision model",
        "搜索模型"
    ]
    
    for intent in test_intents:
        intent_scores = tool_recommender._analyze_user_intent(intent)
        
        print(f"💭 意图: '{intent}'")
        print(f"   分析结果: {intent_scores}")
        
        if ToolCategory.MULTIMODAL in intent_scores:
            print(f"   ✅ 正确识别多模态意图 (分数: {intent_scores[ToolCategory.MULTIMODAL]:.2f})")
        else:
            print(f"   ❌ 未识别多模态意图")
        print()
    
    # 测试完整推荐流程
    print(f"🎯 **完整推荐流程测试**")
    print("-" * 30)
    
    # 分析所有工具
    categorized_tools = tool_recommender.analyze_available_tools(all_tools)
    
    print(f"工具分类结果:")
    for category, tools in categorized_tools.items():
        print(f"  {category}: {len(tools)} 个工具")
        if category == ToolCategory.MULTIMODAL:
            print(f"    多模态工具: {tools}")
    
    # 测试推荐
    multimodal_query = "帮我分析这张图片的内容"
    recommendations = tool_recommender.recommend_tools_for_intent(
        multimodal_query, all_tools, max_recommendations=10
    )
    
    print(f"\n查询: '{multimodal_query}'")
    print(f"推荐结果 ({len(recommendations)} 个):")
    
    for i, rec in enumerate(recommendations, 1):
        print(f"  {i}. {rec['tool_name']} (分数: {rec['score']:.2f}, 类别: {rec['category']})")
        if rec['category'] == ToolCategory.MULTIMODAL:
            print(f"     ✅ 多模态工具推荐成功")
    
    # 检查是否有多模态工具被推荐
    multimodal_recs = [r for r in recommendations if r['category'] == ToolCategory.MULTIMODAL]
    if multimodal_recs:
        print(f"\n✅ 成功推荐 {len(multimodal_recs)} 个多模态工具")
    else:
        print(f"\n❌ 未推荐任何多模态工具")
    
    print(f"\n🎉 OpenRouter工具检测测试完成!")

if __name__ == "__main__":
    asyncio.run(test_openrouter_tool_detection())
