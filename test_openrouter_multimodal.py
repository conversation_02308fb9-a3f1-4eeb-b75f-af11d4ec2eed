#!/usr/bin/env python3
"""
OpenRouter多模态功能测试
"""
import asyncio
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.mcp_client import MCPClient

async def test_openrouter_multimodal():
    """测试OpenRouter多模态功能"""
    print("🎯 OpenRouter 多模态功能测试")
    print("=" * 50)
    
    try:
        # 初始化MCP客户端
        print("📡 正在初始化MCP客户端...")
        mcp_client = MCPClient()
        await mcp_client.initialize()
        
        # 1. 测试模型搜索
        print("\n🔍 测试模型搜索功能...")
        search_result = await mcp_client.call_tool(
            "search_models",
            {
                "query": "vision",
                "capabilities": {
                    "vision": True
                },
                "limit": 5
            }
        )
        
        if search_result and not search_result.get('isError', True):
            content = search_result.get('content', [])
            if content:
                models_data = json.loads(content[0].get('text', '{}'))
                print(f"  ✅ 找到 {len(models_data.get('models', []))} 个视觉模型")
                for model in models_data.get('models', [])[:3]:
                    print(f"    - {model.get('id', 'Unknown')}: {model.get('name', 'No name')}")
        else:
            print("  ❌ 模型搜索失败")
            if search_result:
                print(f"    错误详情: {search_result}")
            else:
                print(f"    无返回结果")
        
        # 2. 测试获取特定模型信息
        print("\n📋 测试获取模型信息...")
        model_info_result = await mcp_client.call_tool(
            "get_model_info",
            {
                "model": "qwen/qwen2.5-vl-32b-instruct:free"
            }
        )
        
        if model_info_result and not model_info_result.get('isError', True):
            content = model_info_result.get('content', [])
            if content:
                model_data = json.loads(content[0].get('text', '{}'))
                print(f"  ✅ 模型信息获取成功:")
                print(f"    - 名称: {model_data.get('name', 'Unknown')}")
                print(f"    - 上下文长度: {model_data.get('context_length', 'Unknown')}")
                print(f"    - 支持视觉: {model_data.get('architecture', {}).get('modality', 'Unknown')}")
        else:
            print("  ❌ 模型信息获取失败")
            if model_info_result:
                print(f"    错误详情: {model_info_result}")
            else:
                print(f"    无返回结果")
        
        # 3. 测试聊天完成（文本）
        print("\n💬 测试文本聊天完成...")
        chat_result = await mcp_client.call_tool(
            "chat_completion",
            {
                "model": "qwen/qwen2.5-vl-32b-instruct:free",
                "messages": [
                    {
                        "role": "user",
                        "content": "请用中文简单介绍一下DaVinci Resolve的主要功能。"
                    }
                ],
                "max_tokens": 200,
                "temperature": 0.7
            }
        )
        
        if chat_result and not chat_result.get('isError', True):
            content = chat_result.get('content', [])
            if content:
                response_data = json.loads(content[0].get('text', '{}'))
                choices = response_data.get('choices', [])
                if choices:
                    message = choices[0].get('message', {}).get('content', '')
                    print(f"  ✅ 聊天完成成功:")
                    print(f"    回复: {message[:100]}...")
                else:
                    print("  ⚠️  响应格式异常")
                    print(f"    响应数据: {response_data}")
        else:
            print("  ❌ 聊天完成失败")
            if chat_result:
                print(f"    错误详情: {chat_result}")
            else:
                print(f"    无返回结果")
        
        # 4. 测试模型验证
        print("\n✅ 测试模型验证...")
        validate_result = await mcp_client.call_tool(
            "validate_model",
            {
                "model": "qwen/qwen2.5-vl-32b-instruct:free"
            }
        )
        
        if validate_result and not validate_result.get('isError', True):
            print("  ✅ 模型验证成功")
        else:
            print("  ❌ 模型验证失败")
            if validate_result:
                print(f"    错误详情: {validate_result}")
            else:
                print(f"    无返回结果")
        
        print("\n🎉 OpenRouter多模态功能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await test_openrouter_multimodal()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
