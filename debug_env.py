#!/usr/bin/env python3
"""
调试环境变量加载
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.unified_config import ConfigManager

def debug_environment():
    """调试环境变量"""
    print("🔍 环境变量调试")
    print("=" * 50)
    
    # 1. 检查.env文件
    env_file = project_root / '.env'
    print(f"📁 .env文件路径: {env_file}")
    print(f"📁 .env文件存在: {env_file.exists()}")
    
    if env_file.exists():
        print("\n📄 .env文件内容 (OPENROUTER相关):")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if 'OPENROUTER' in line:
                    print(f"  {line_num}: {line.strip()}")
    
    # 2. 检查os.environ
    print(f"\n🌍 os.environ中的OPENROUTER_API_KEY:")
    openrouter_key = os.getenv('OPENROUTER_API_KEY')
    if openrouter_key:
        print(f"  ✅ 已设置: {openrouter_key[:20]}...")
    else:
        print(f"  ❌ 未设置")
    
    # 3. 检查ConfigManager
    print(f"\n⚙️ ConfigManager环境变量:")
    config_manager = ConfigManager()
    print(f"  加载的环境变量数量: {len(config_manager._env_vars)}")
    
    openrouter_from_config = config_manager._env_vars.get('OPENROUTER_API_KEY')
    if openrouter_from_config:
        print(f"  ✅ ConfigManager中的OPENROUTER_API_KEY: {openrouter_from_config[:20]}...")
    else:
        print(f"  ❌ ConfigManager中未找到OPENROUTER_API_KEY")
    
    # 4. 手动加载.env并设置到os.environ
    print(f"\n🔧 手动加载.env到os.environ:")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    if key == 'OPENROUTER_API_KEY':
                        os.environ[key] = value
                        print(f"  ✅ 设置 {key} = {value[:20]}...")
    
    # 5. 再次检查os.environ
    print(f"\n🌍 设置后os.environ中的OPENROUTER_API_KEY:")
    openrouter_key_after = os.getenv('OPENROUTER_API_KEY')
    if openrouter_key_after:
        print(f"  ✅ 已设置: {openrouter_key_after[:20]}...")
    else:
        print(f"  ❌ 仍未设置")

if __name__ == "__main__":
    debug_environment()
