import { computed, ref } from 'vue'

// 定义步骤状态类型
export type StepStatus = 'pending' | 'processing' | 'completed' | 'error'

// 定义步骤接口
export interface TaskStep {
  id: string
  title: string
  description: string
  status: StepStatus
  progress?: number
  error?: string
  duration?: number
  startTime?: Date
  endTime?: Date
  details?: {
    tool?: string
    server?: string
    parameters?: Record<string, any>
    result?: any
    aiContent?: string
    showFullContent?: boolean
    toolCalls?: Array<{
      name: string
      server: string
      arguments: Record<string, any>
      result: any
      success: boolean
      error?: string
      duration_ms: number
    }>
  }
}

// 定义任务接口
export interface Task {
  id: string
  title: string
  description: string
  steps: TaskStep[]
  currentStepIndex: number
  status: 'pending' | 'running' | 'completed' | 'error' | 'cancelled'
  progress?: number
  startTime?: Date
  endTime?: Date
  result?: any
  error?: string
}

/**
 * AI任务进度管理组合式函数
 */
export function useTaskProgress() {
  // 当前任务
  const currentTask = ref<Task | null>(null)

  // 任务历史
  const taskHistory = ref<Task[]>([])

  // 是否显示进度界面
  const showProgress = ref(false)

  // 计算属性
  const isTaskRunning = computed(() =>
    currentTask.value?.status === 'running'
  )

  const canCancel = computed(() =>
    currentTask.value?.status === 'running'
  )

  const canRetry = computed(() =>
    currentTask.value?.status === 'error'
  )

  /**
   * 创建新任务
   */
  function createTask(
    title: string,
    description: string,
    stepDefinitions: Omit<TaskStep, 'status' | 'startTime' | 'endTime'>[]
  ): Task {
    const task: Task = {
      id: generateTaskId(),
      title,
      description,
      steps: stepDefinitions.map(step => ({
        ...step,
        status: 'pending' as StepStatus
      })),
      currentStepIndex: 0,
      status: 'pending',
      startTime: new Date()
    }

    return task
  }

  /**
   * 开始执行任务
   */
  function startTask(task: Task) {
    currentTask.value = task
    currentTask.value.status = 'running'
    currentTask.value.startTime = new Date()
    showProgress.value = true

    // 开始第一个步骤
    if (task.steps.length > 0) {
      startStep(0)
    }
  }

  /**
   * 开始执行指定步骤
   */
  function startStep(stepIndex: number) {
    if (!currentTask.value || stepIndex >= currentTask.value.steps.length) return

    const step = currentTask.value.steps[stepIndex]
    step.status = 'processing'
    step.startTime = new Date()
    step.progress = 0

    currentTask.value.currentStepIndex = stepIndex
  }

  /**
   * 更新步骤进度
   */
  function updateStepProgress(stepIndex: number, progress: number, details?: Partial<TaskStep>) {
    if (!currentTask.value || stepIndex >= currentTask.value.steps.length) return

    const step = currentTask.value.steps[stepIndex]
    step.progress = Math.min(100, Math.max(0, progress))

    if (details) {
      Object.assign(step, details)
    }
  }

  /**
   * 完成步骤
   */
  function completeStep(stepIndex: number, result?: any) {
    if (!currentTask.value || stepIndex >= currentTask.value.steps.length) return

    const step = currentTask.value.steps[stepIndex]
    step.status = 'completed'
    step.progress = 100
    step.endTime = new Date()

    if (step.startTime) {
      step.duration = step.endTime.getTime() - step.startTime.getTime()
    }

    if (result) {
      step.details = { ...step.details, result }
    }

    // 检查是否还有下一步
    const nextStepIndex = stepIndex + 1
    if (nextStepIndex < currentTask.value.steps.length) {
      startStep(nextStepIndex)
    } else {
      completeTask(result)
    }
  }

  /**
   * 步骤执行失败
   */
  function failStep(stepIndex: number, error: string) {
    if (!currentTask.value || stepIndex >= currentTask.value.steps.length) return

    const step = currentTask.value.steps[stepIndex]
    step.status = 'error'
    step.error = error
    step.endTime = new Date()

    if (step.startTime) {
      step.duration = step.endTime.getTime() - step.startTime.getTime()
    }

    // 任务失败
    failTask(error)
  }

  /**
   * 完成任务
   */
  function completeTask(result?: any) {
    if (!currentTask.value) return

    currentTask.value.status = 'completed'
    currentTask.value.endTime = new Date()
    currentTask.value.result = result

    // 添加到历史记录
    taskHistory.value.unshift({ ...currentTask.value })

    // 保持历史记录在合理数量
    if (taskHistory.value.length > 50) {
      taskHistory.value = taskHistory.value.slice(0, 50)
    }
  }

  /**
   * 任务失败
   */
  function failTask(error: string) {
    if (!currentTask.value) return

    currentTask.value.status = 'error'
    currentTask.value.error = error
    currentTask.value.endTime = new Date()

    // 添加到历史记录
    taskHistory.value.unshift({ ...currentTask.value })
  }

  /**
   * 取消任务
   */
  function cancelTask() {
    if (!currentTask.value) return

    currentTask.value.status = 'cancelled'
    currentTask.value.endTime = new Date()

    // 取消当前步骤
    const currentStep = currentTask.value.steps[currentTask.value.currentStepIndex]
    if (currentStep && currentStep.status === 'processing') {
      currentStep.status = 'error'
      currentStep.error = '用户取消'
      currentStep.endTime = new Date()
    }

    // 添加到历史记录
    taskHistory.value.unshift({ ...currentTask.value })

    hideProgress()
  }

  /**
   * 重试任务
   */
  function retryTask() {
    if (!currentTask.value) return

    // 重置所有步骤状态
    currentTask.value.steps.forEach(step => {
      step.status = 'pending'
      step.progress = undefined
      step.error = undefined
      step.startTime = undefined
      step.endTime = undefined
      step.duration = undefined
    })

    // 重置任务状态
    currentTask.value.status = 'running'
    currentTask.value.currentStepIndex = 0
    currentTask.value.startTime = new Date()
    currentTask.value.endTime = undefined
    currentTask.value.error = undefined

    // 开始第一个步骤
    if (currentTask.value.steps.length > 0) {
      startStep(0)
    }
  }

  /**
   * 隐藏进度界面
   */
  function hideProgress() {
    showProgress.value = false
  }

  /**
   * 清除当前任务
   */
  function clearCurrentTask() {
    currentTask.value = null
    showProgress.value = false
  }

  /**
   * 生成任务ID
   */
  function generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 创建标准的AI处理任务步骤
   */
  function createAIProcessingSteps(query: string): Omit<TaskStep, 'status' | 'startTime' | 'endTime'>[] {
    return [
      {
        id: 'analyze',
        title: '分析用户需求',
        description: '正在分析您的自然语言请求...',
        details: { parameters: { query } }
      },
      {
        id: 'select_tool',
        title: '选择合适工具',
        description: '根据需求分析结果选择最佳工具...'
      },
      {
        id: 'prepare',
        title: '准备执行参数',
        description: '配置工具参数和执行环境...'
      },
      {
        id: 'execute',
        title: '执行AI任务',
        description: '调用AI服务执行任务...'
      },
      {
        id: 'process_result',
        title: '处理执行结果',
        description: '整理和格式化执行结果...'
      }
    ]
  }

  return {
    // 状态
    currentTask: computed(() => currentTask.value),
    taskHistory: computed(() => taskHistory.value),
    showProgress: computed(() => showProgress.value),
    isTaskRunning,
    canCancel,
    canRetry,

    // 方法
    createTask,
    startTask,
    startStep,
    updateStepProgress,
    completeStep,
    failStep,
    completeTask,
    failTask,
    cancelTask,
    retryTask,
    hideProgress,
    clearCurrentTask,
    createAIProcessingSteps
  }
}
