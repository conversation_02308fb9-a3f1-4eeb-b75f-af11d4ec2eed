<template>
  <div class="ai-task-progress">
    <!-- 任务执行状态卡片 -->
    <div v-if="isVisible" class="progress-card bg-davinci-card border border-davinci-border rounded-lg p-6 mb-6">
      <!-- 头部：任务标题和状态 -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <div class="relative">
            <div 
              :class="[
                'w-4 h-4 rounded-full',
                currentStep.status === 'processing' ? 'bg-blue-500 animate-pulse' :
                currentStep.status === 'completed' ? 'bg-green-500' :
                currentStep.status === 'error' ? 'bg-red-500' : 'bg-gray-500'
              ]"
            ></div>
            <div 
              v-if="currentStep.status === 'processing'"
              class="absolute inset-0 w-4 h-4 rounded-full bg-blue-500 animate-ping opacity-75"
            ></div>
          </div>
          <h3 class="text-lg font-medium">{{ taskTitle }}</h3>
        </div>
        
        <div class="flex items-center space-x-2">
          <span class="text-sm text-davinci-muted">
            {{ currentStepIndex + 1 }}/{{ steps.length }}
          </span>
          <el-button 
            v-if="canCancel && currentStep.status === 'processing'"
            @click="cancelTask"
            size="small"
            type="danger"
            text
          >
            取消
          </el-button>
        </div>
      </div>

      <!-- 整体进度条 -->
      <div class="mb-6">
        <div class="flex justify-between items-center mb-2">
          <span class="text-sm font-medium">整体进度</span>
          <span class="text-sm text-davinci-muted">{{ Math.round(overallProgress) }}%</span>
        </div>
        <el-progress 
          :percentage="overallProgress"
          :status="overallStatus"
          :stroke-width="8"
          :show-text="false"
        />
      </div>

      <!-- 步骤列表 -->
      <div class="steps-container">
        <div class="text-sm font-medium mb-3">执行步骤</div>
        <div class="space-y-3">
          <div 
            v-for="(step, index) in steps" 
            :key="step.id"
            :class="[
              'step-item flex items-start space-x-3 p-3 rounded-lg transition-all duration-300',
              index === currentStepIndex ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' :
              step.status === 'completed' ? 'bg-green-50 dark:bg-green-900/20' :
              step.status === 'error' ? 'bg-red-50 dark:bg-red-900/20' :
              'bg-gray-50 dark:bg-gray-800/50'
            ]"
          >
            <!-- 步骤图标 -->
            <div class="flex-shrink-0 mt-0.5">
              <div 
                v-if="step.status === 'processing'"
                class="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"
              ></div>
              <div 
                v-else-if="step.status === 'completed'"
                class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
              >
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div 
                v-else-if="step.status === 'error'"
                class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center"
              >
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div 
                v-else
                class="w-5 h-5 bg-gray-400 rounded-full"
              ></div>
            </div>

            <!-- 步骤内容 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium">{{ step.title }}</h4>
                <span 
                  v-if="step.status === 'processing' && step.progress !== undefined"
                  class="text-xs text-davinci-muted"
                >
                  {{ step.progress }}%
                </span>
              </div>
              
              <p class="text-xs text-davinci-muted mt-1">{{ step.description }}</p>
              
              <!-- 步骤详细信息 -->
              <div v-if="step.details" class="mt-2">
                <div v-if="step.details.tool" class="text-xs">
                  <span class="text-davinci-muted">工具:</span>
                  <span class="ml-1 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded">
                    {{ step.details.tool }}
                  </span>
                </div>
                <div v-if="step.details.server" class="text-xs mt-1">
                  <span class="text-davinci-muted">服务器:</span>
                  <span class="ml-1">{{ step.details.server }}</span>
                </div>

                <!-- 实时工具调用详情 -->
                <div v-if="step.details.toolCalls && step.details.toolCalls.length > 0" class="mt-3 p-2 bg-gray-50 dark:bg-gray-800/50 rounded">
                  <div class="text-xs font-medium text-davinci-muted mb-2">🔧 工具调用详情</div>
                  <div v-for="(call, callIndex) in step.details.toolCalls" :key="callIndex" class="mb-2 last:mb-0">
                    <div class="flex items-center justify-between mb-1">
                      <span class="text-xs font-medium">{{ call.name }}</span>
                      <span class="text-xs px-1.5 py-0.5 rounded" :class="call.success ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300' : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'">
                        {{ call.success ? '成功' : '失败' }}
                      </span>
                    </div>
                    <div v-if="call.arguments && Object.keys(call.arguments).length > 0" class="text-xs text-davinci-muted mb-1">
                      <span class="font-medium">参数:</span>
                      <pre class="mt-1 p-1 bg-white dark:bg-gray-900 rounded text-xs overflow-x-auto">{{ JSON.stringify(call.arguments, null, 2) }}</pre>
                    </div>
                    <div v-if="call.result" class="text-xs text-davinci-muted">
                      <span class="font-medium">结果:</span>
                      <!-- 媒体文件显示 -->
                      <div v-if="isMediaFile(call.result)" class="mt-2">
                        <div v-if="isAudioFile(call.result)" class="audio-player">
                          <audio controls class="w-full max-w-sm">
                            <source :src="getMediaUrl(call.result)" type="audio/mpeg">
                            您的浏览器不支持音频播放。
                          </audio>
                          <div class="text-xs text-gray-500 mt-1">🎵 {{ getFileName(call.result) }}</div>
                        </div>
                        <div v-else-if="isImageFile(call.result)" class="image-display">
                          <img :src="getMediaUrl(call.result)" :alt="getFileName(call.result)"
                               class="max-w-full max-h-32 rounded border object-cover">
                          <div class="text-xs text-gray-500 mt-1">🖼️ {{ getFileName(call.result) }}</div>
                        </div>
                        <div v-else-if="isVideoFile(call.result)" class="video-player">
                          <video controls class="w-full max-w-sm max-h-32">
                            <source :src="getMediaUrl(call.result)" type="video/mp4">
                            您的浏览器不支持视频播放。
                          </video>
                          <div class="text-xs text-gray-500 mt-1">🎬 {{ getFileName(call.result) }}</div>
                        </div>
                      </div>
                      <!-- 文本结果显示 -->
                      <div v-else class="mt-1 p-1 bg-white dark:bg-gray-900 rounded text-xs">{{ formatToolResult(call.result) }}</div>
                    </div>
                  </div>
                </div>

                <!-- 实时AI生成内容 -->
                <div v-if="step.details.aiContent" class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                  <div class="flex items-center justify-between mb-2">
                    <div class="text-xs font-medium text-blue-700 dark:text-blue-300">🤖 AI生成内容</div>
                    <button
                      v-if="step.details.aiContent.length > 200 && !step.details.showFullContent"
                      @click="step.details.showFullContent = true"
                      class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                    >
                      展开全部
                    </button>
                  </div>
                  <div class="text-sm text-gray-800 dark:text-gray-200">
                    <div v-if="step.status === 'processing'" class="relative">
                      <div class="whitespace-pre-wrap">{{ step.details.aiContent }}</div>
                      <div class="inline-flex items-center ml-1">
                        <div class="typing-indicator">
                          <span class="dot"></span>
                          <span class="dot"></span>
                          <span class="dot"></span>
                        </div>
                      </div>
                    </div>
                    <div v-else class="whitespace-pre-wrap">
                      <div v-if="step.details.showFullContent || step.details.aiContent.length <= 200">
                        {{ step.details.aiContent }}
                      </div>
                      <div v-else>
                        {{ step.details.aiContent.substring(0, 200) }}...
                        <button
                          @click="step.details.showFullContent = true"
                          class="ml-2 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 underline"
                        >
                          查看完整内容
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 步骤进度条 -->
              <div v-if="step.status === 'processing' && step.progress !== undefined" class="mt-2">
                <el-progress 
                  :percentage="step.progress"
                  :stroke-width="4"
                  :show-text="false"
                  status="success"
                />
              </div>

              <!-- 错误信息 -->
              <div v-if="step.status === 'error' && step.error" class="mt-2">
                <div class="text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                  <div class="font-medium mb-1">❌ 执行失败</div>
                  <div class="whitespace-pre-line">{{ step.error }}</div>
                  <div v-if="step.error.includes('超时')" class="mt-2 text-xs text-orange-600 dark:text-orange-400">
                    💡 提示: AI处理可能需要更长时间，建议点击重试按钮
                  </div>
                </div>
              </div>

              <!-- 执行时间 -->
              <div v-if="step.duration" class="text-xs text-davinci-muted mt-1">
                耗时: {{ formatDuration(step.duration) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区 -->
      <div v-if="showActions" class="flex items-center justify-between mt-6 pt-4 border-t border-davinci-border">
        <div class="text-sm text-davinci-muted">
          <span v-if="startTime">
            开始时间: {{ formatTime(startTime) }}
          </span>
          <span v-if="endTime" class="ml-4">
            完成时间: {{ formatTime(endTime) }}
          </span>
        </div>
        
        <div class="flex space-x-2">
          <el-button 
            v-if="canRetry && currentStep.status === 'error'"
            @click="retryTask"
            size="small"
            type="warning"
          >
            重试
          </el-button>
          <el-button 
            v-if="isCompleted"
            @click="viewResult"
            size="small"
            type="primary"
          >
            查看结果
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义步骤状态类型
type StepStatus = 'pending' | 'processing' | 'completed' | 'error'

// 定义步骤接口
interface TaskStep {
  id: string
  title: string
  description: string
  status: StepStatus
  progress?: number
  error?: string
  duration?: number
  details?: {
    tool?: string
    server?: string
    parameters?: Record<string, any>
    toolCalls?: Array<{
      name: string
      server: string
      arguments: Record<string, any>
      result: string
      success: boolean
      error?: string
      duration_ms: number
    }>
    aiContent?: string
    showFullContent?: boolean
  }
}

// Props
interface Props {
  visible?: boolean
  title?: string
  steps?: TaskStep[]
  currentStep?: number
  canCancel?: boolean
  canRetry?: boolean
  showActions?: boolean
  startTime?: Date
  endTime?: Date
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: 'AI任务执行中',
  steps: () => [],
  currentStep: 0,
  canCancel: true,
  canRetry: true,
  showActions: true
})

// Emits
const emit = defineEmits<{
  cancel: []
  retry: []
  viewResult: []
}>()

// 响应式数据
const isVisible = computed(() => props.visible)
const taskTitle = computed(() => props.title)
const steps = computed(() => props.steps)
const currentStepIndex = computed(() => props.currentStep)
const currentStep = computed(() => steps.value[currentStepIndex.value] || { status: 'pending' })

// 计算整体进度 - 使用加权进度系统
const overallProgress = computed(() => {
  if (steps.value.length === 0) return 0

  // 定义步骤权重（基于实际耗时分析）- 修复步骤名称匹配
  const stepWeights = {
    'analyze': 1,             // 分析用户需求：1%
    'select_tool': 2,         // 选择合适工具：2%
    'prepare': 2,             // 准备执行参数：2%
    'execute': 90,            // 执行AI任务：90%（最耗时）
    'process_result': 5       // 处理执行结果：5%
  }



  let totalProgress = 0
  let totalWeight = 0

  for (let i = 0; i < steps.value.length; i++) {
    const step = steps.value[i]
    const stepName = step.id || `step_${i}`
    const weight = stepWeights[stepName as keyof typeof stepWeights] || (100 / steps.value.length) // 默认等权重

    totalWeight += weight

    if (step.status === 'completed') {
      totalProgress += weight
    } else if (step.status === 'processing') {
      const stepProgress = step.progress || 0
      totalProgress += (weight * stepProgress / 100)
    }
  }

  // 如果所有步骤都完成了，返回100%
  if (steps.value.every(step => step.status === 'completed')) {
    return 100
  }

  // 计算加权进度百分比
  const progress = totalWeight > 0 ? (totalProgress / totalWeight) * 100 : 0
  return Math.min(100, Math.max(0, progress))
})

// 计算整体状态
const overallStatus = computed(() => {
  if (steps.value.some(step => step.status === 'error')) return 'exception'
  if (steps.value.every(step => step.status === 'completed')) return 'success'
  return undefined
})

// 是否已完成
const isCompleted = computed(() => {
  return steps.value.length > 0 && steps.value.every(step => 
    step.status === 'completed' || step.status === 'error'
  )
})

// 方法
function cancelTask() {
  emit('cancel')
}

function retryTask() {
  emit('retry')
}

function viewResult() {
  emit('viewResult')
}

function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`
}

function formatToolResult(result: any): string {
  if (typeof result === 'string') {
    return result.length > 200 ? result.substring(0, 200) + '...' : result
  } else if (typeof result === 'object') {
    const str = JSON.stringify(result, null, 2)
    return str.length > 200 ? str.substring(0, 200) + '...' : str
  }
  return String(result)
}

function formatTime(date: Date): string {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 媒体文件处理函数
function isMediaFile(result: any): boolean {
  // 处理字符串格式
  if (typeof result === 'string') {
    const lowerResult = result.toLowerCase()
    return lowerResult.includes('.mp3') || lowerResult.includes('.wav') ||
           lowerResult.includes('.mp4') || lowerResult.includes('.avi') ||
           lowerResult.includes('.jpg') || lowerResult.includes('.jpeg') ||
           lowerResult.includes('.png') || lowerResult.includes('.gif') ||
           lowerResult.includes('.webp') || lowerResult.includes('.m4a')
  }

  // 处理数组格式（MCP工具调用结果）
  if (Array.isArray(result)) {
    return result.some(item => {
      if (item && typeof item.text === 'string') {
        const lowerText = item.text.toLowerCase()
        return lowerText.includes('.mp3') || lowerText.includes('.wav') ||
               lowerText.includes('.mp4') || lowerText.includes('.avi') ||
               lowerText.includes('.jpg') || lowerText.includes('.jpeg') ||
               lowerText.includes('.png') || lowerText.includes('.gif') ||
               lowerText.includes('.webp') || lowerText.includes('.m4a')
      }
      return false
    })
  }

  return false
}

function isAudioFile(result: any): boolean {
  // 处理字符串格式
  if (typeof result === 'string') {
    const lowerResult = result.toLowerCase()
    return lowerResult.includes('.mp3') || lowerResult.includes('.wav') || lowerResult.includes('.m4a')
  }

  // 处理数组格式（MCP工具调用结果）
  if (Array.isArray(result)) {
    return result.some(item => {
      if (item && typeof item.text === 'string') {
        const lowerText = item.text.toLowerCase()
        return lowerText.includes('.mp3') || lowerText.includes('.wav') || lowerText.includes('.m4a')
      }
      return false
    })
  }

  return false
}

function isImageFile(result: any): boolean {
  // 处理字符串格式
  if (typeof result === 'string') {
    const lowerResult = result.toLowerCase()
    return lowerResult.includes('.jpg') || lowerResult.includes('.jpeg') ||
           lowerResult.includes('.png') || lowerResult.includes('.gif') ||
           lowerResult.includes('.webp')
  }

  // 处理数组格式（MCP工具调用结果）
  if (Array.isArray(result)) {
    return result.some(item => {
      if (item && typeof item.text === 'string') {
        const lowerText = item.text.toLowerCase()
        return lowerText.includes('.jpg') || lowerText.includes('.jpeg') ||
               lowerText.includes('.png') || lowerText.includes('.gif') ||
               lowerText.includes('.webp')
      }
      return false
    })
  }

  return false
}

function isVideoFile(result: any): boolean {
  // 处理字符串格式
  if (typeof result === 'string') {
    const lowerResult = result.toLowerCase()
    return lowerResult.includes('.mp4') || lowerResult.includes('.avi') ||
           lowerResult.includes('.mov') || lowerResult.includes('.webm')
  }

  // 处理数组格式（MCP工具调用结果）
  if (Array.isArray(result)) {
    return result.some(item => {
      if (item && typeof item.text === 'string') {
        const lowerText = item.text.toLowerCase()
        return lowerText.includes('.mp4') || lowerText.includes('.avi') ||
               lowerText.includes('.mov') || lowerText.includes('.webm')
      }
      return false
    })
  }

  return false
}

function getMediaUrl(result: any): string {
  let textContent = ''

  // 处理字符串格式
  if (typeof result === 'string') {
    textContent = result
  }
  // 处理数组格式（MCP工具调用结果）
  else if (Array.isArray(result)) {
    const textItem = result.find(item => item && typeof item.text === 'string')
    if (textItem) {
      textContent = textItem.text
    }
  }

  if (!textContent) return ''

  // 如果结果包含文件路径，提取文件名并构建URL
  const match = textContent.match(/([^\/\\]+\.(mp3|wav|m4a|mp4|avi|mov|webm|jpg|jpeg|png|gif|webp))/)
  if (match) {
    const fileName = match[1]
    return `/user-output/${fileName}`
  }

  // 如果结果本身就是文件名
  if (textContent.match(/\.(mp3|wav|m4a|mp4|avi|mov|webm|jpg|jpeg|png|gif|webp)$/i)) {
    return `/user-output/${textContent}`
  }

  return textContent
}

function getFileName(result: any): string {
  let textContent = ''

  // 处理字符串格式
  if (typeof result === 'string') {
    textContent = result
  }
  // 处理数组格式（MCP工具调用结果）
  else if (Array.isArray(result)) {
    const textItem = result.find(item => item && typeof item.text === 'string')
    if (textItem) {
      textContent = textItem.text
    }
  }

  if (!textContent) return '未知文件'

  // 提取文件名
  const match = textContent.match(/([^\/\\]+\.(mp3|wav|m4a|mp4|avi|mov|webm|jpg|jpeg|png|gif|webp))/)
  if (match) {
    return match[1]
  }

  // 如果结果本身就是文件名
  if (textContent.match(/\.(mp3|wav|m4a|mp4|avi|mov|webm|jpg|jpeg|png|gif|webp)$/i)) {
    return textContent
  }

  return textContent.length > 30 ? textContent.substring(0, 30) + '...' : textContent
}
</script>

<style scoped>
.ai-task-progress {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.progress-card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.step-item {
  transition: all 0.3s ease;
}

.step-item:hover {
  transform: translateY(-1px);
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-card {
  animation: slideIn 0.3s ease-out;
}

/* 打字机效果样式 */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  margin-left: 4px;
}

.typing-indicator .dot {
  width: 4px;
  height: 4px;
  background-color: #3b82f6;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
