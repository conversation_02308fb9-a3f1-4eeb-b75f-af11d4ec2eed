<template>
  <div class="mcp-interface bg-davinci-bg min-h-screen text-davinci-text">
    <!-- 头部状态栏 -->
    <div class="bg-davinci-card border-b border-davinci-border p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h1 class="text-2xl font-bold text-davinci-text">
            🎬 DaVinci AI Co-pilot Pro
          </h1>
          <span class="text-sm text-davinci-muted">v2.0.0</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- 连接状态 -->
          <div class="flex items-center space-x-2">
            <div 
              :class="[
                'w-3 h-3 rounded-full',
                isReady ? 'bg-davinci-success animate-pulse-slow' : 'bg-davinci-error'
              ]"
            ></div>
            <span class="text-sm">
              {{ isReady ? '已连接' : '连接中...' }}
            </span>
          </div>
          
          <!-- 统计信息 -->
          <div class="text-sm text-davinci-muted">
            {{ stats.connectedServers }}/{{ stats.totalServers }} 服务器 | 
            {{ stats.totalTools }} 工具
          </div>
          
          <!-- 刷新按钮 -->
          <el-button 
            @click="refresh" 
            :loading="isProcessing"
            size="small"
            type="primary"
          >
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex h-[calc(100vh-80px)]">
      <!-- 左侧：自然语言输入 -->
      <div class="flex-1 p-6 overflow-y-auto">
        <!-- AI任务进度展示 -->
        <AITaskProgress
          v-if="currentTask"
          :visible="showProgress"
          :title="currentTask.title"
          :steps="currentTask.steps"
          :current-step="currentTask.currentStepIndex"
          :can-cancel="canCancel"
          :can-retry="canRetry"
          :start-time="currentTask.startTime"
          :end-time="currentTask.endTime"
          @cancel="cancelTask"
          @retry="retryTask"
          @view-result="showResultDialog"
        />
        <div class="bg-davinci-card rounded-lg p-6 h-full flex flex-col">
          <h2 class="text-xl font-semibold mb-4 flex items-center">
            🤖 智能助手
            <el-tag 
              v-if="isProcessing" 
              type="warning" 
              size="small" 
              class="ml-2"
            >
              处理中
            </el-tag>
          </h2>
          
          <!-- 输入区域 -->
          <div class="flex-1 flex flex-col">
            <el-input
              v-model="userInput"
              type="textarea"
              :rows="6"
              placeholder="请用自然语言描述您的需求，例如：&#10;• 帮我生成一段关于AI的语音&#10;• 创建一个新的DaVinci项目&#10;• 分析这段文本的情感倾向"
              class="mb-4"
              :disabled="isProcessing"
              @keydown.ctrl.enter="handleSubmit"
            />
            
            <!-- 工具建议 -->
            <div v-if="suggestions.length > 0" class="mb-4">
              <div class="text-sm text-davinci-muted mb-2">建议的工具：</div>
              <div class="flex flex-wrap gap-2">
                <el-tag
                  v-for="suggestion in suggestions"
                  :key="suggestion"
                  size="small"
                  type="info"
                  class="cursor-pointer"
                  @click="userInput += ` 使用${suggestion}工具`"
                >
                  {{ suggestion }}
                </el-tag>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex space-x-3">
              <el-button 
                type="primary" 
                @click="handleSubmit"
                :loading="isProcessing"
                :disabled="!userInput.trim() || !isReady"
                class="flex-1"
              >
                <template #loading>
                  <div class="flex items-center">
                    <el-icon class="animate-spin mr-2"><Loading /></el-icon>
                    处理中...
                  </div>
                </template>
                {{ isProcessing ? '处理中...' : '智能处理' }}
              </el-button>
              
              <el-button @click="clearInput" :disabled="isProcessing">
                清空
              </el-button>
              
              <el-button 
                v-if="lastResult && !lastResult.success"
                @click="retryLastRequest"
                type="warning"
                :loading="isProcessing"
              >
                重试
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：结果展示和工具列表 -->
      <div class="w-96 p-6 border-l border-davinci-border">
        <el-tabs v-model="activeTab" class="h-full">
          <!-- 处理结果 -->
          <el-tab-pane label="处理结果" name="results" class="h-full">
            <div class="h-full flex flex-col">
              <div v-if="lastResult" class="mb-4">
                <div 
                  :class="[
                    'p-4 rounded-lg',
                    lastResult.success ? 'bg-green-900/20 border border-green-500/30' : 'bg-red-900/20 border border-red-500/30'
                  ]"
                >
                  <div class="flex items-center justify-between mb-2">
                    <span :class="lastResult.success ? 'text-green-400' : 'text-red-400'">
                      {{ lastResult.success ? '✅ 处理成功' : '❌ 处理失败' }}
                    </span>
                    <span class="text-xs text-davinci-muted">
                      {{ lastResult.processing_time }}ms
                    </span>
                  </div>
                  
                  <div class="text-sm">
                    <pre class="whitespace-pre-wrap font-mono text-xs">{{ formatResult(lastResult) }}</pre>
                  </div>
                  
                  <div v-if="lastResult.tools_used?.length" class="mt-2">
                    <div class="text-xs text-davinci-muted mb-1">使用的工具:</div>
                    <div class="flex flex-wrap gap-1">
                      <el-tag 
                        v-for="tool in lastResult.tools_used" 
                        :key="tool"
                        size="small"
                        type="success"
                      >
                        {{ tool }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 历史记录 -->
              <div class="flex-1 overflow-y-auto">
                <h3 class="text-sm font-medium mb-2 text-davinci-muted">处理历史</h3>
                <div class="space-y-2">
                  <div 
                    v-for="(result, index) in history" 
                    :key="index"
                    :class="[
                      'p-3 rounded text-xs',
                      result.success ? 'bg-green-900/10' : 'bg-red-900/10'
                    ]"
                  >
                    <div class="flex items-center justify-between mb-1">
                      <span :class="result.success ? 'text-green-400' : 'text-red-400'">
                        {{ result.success ? '✅' : '❌' }}
                      </span>
                      <span class="text-davinci-muted">
                        {{ new Date(result.timestamp || '').toLocaleTimeString() }}
                      </span>
                    </div>
                    <div class="text-davinci-muted truncate">
                      {{ result.success ? '处理成功' : result.error }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 可用工具 -->
          <el-tab-pane label="可用工具" name="tools">
            <div class="h-full overflow-y-auto">
              <div class="mb-4">
                <el-input
                  v-model="toolFilter"
                  placeholder="搜索工具..."
                  size="small"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              
              <div class="space-y-4">
                <div v-for="(tools, serverName) in filteredToolsByServer" :key="serverName">
                  <h3 class="text-sm font-medium text-davinci-muted mb-2">
                    {{ serverName }} ({{ tools.length }})
                  </h3>
                  <div class="space-y-2">
                    <div 
                      v-for="tool in tools" 
                      :key="tool.name"
                      class="p-3 bg-davinci-bg rounded border border-davinci-border hover:border-primary-500 transition-colors cursor-pointer"
                      @click="insertTool(tool.name)"
                    >
                      <div class="font-medium text-sm">{{ tool.name }}</div>
                      <div class="text-xs text-davinci-muted mt-1">
                        {{ tool.description }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 服务器状态 -->
          <el-tab-pane label="服务器" name="servers">
            <div class="space-y-3">
              <div 
                v-for="server in servers" 
                :key="server.name"
                class="p-3 bg-davinci-bg rounded border border-davinci-border"
              >
                <div class="flex items-center justify-between mb-2">
                  <span class="font-medium">{{ server.name }}</span>
                  <div 
                    :class="[
                      'w-2 h-2 rounded-full',
                      server.connected ? 'bg-davinci-success' : 'bg-davinci-error'
                    ]"
                  ></div>
                </div>
                <div class="text-xs text-davinci-muted">
                  {{ server.tool_count }} 个工具
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 错误提示 -->
    <el-dialog v-model="showError" title="错误" width="400px">
      <p>{{ error }}</p>
      <template #footer>
        <el-button @click="clearError">确定</el-button>
      </template>
    </el-dialog>

    <!-- 结果查看对话框 -->
    <el-dialog
      v-model="resultDialogVisible"
      title="AI处理结果"
      width="80%"
      :close-on-click-modal="false"
      class="result-dialog"
    >
      <div v-if="currentTask?.result" class="result-content">
        <!-- 基本信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-3">📋 处理摘要</h3>
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="font-medium">任务状态：</span>
                <el-tag :type="currentTask.status === 'completed' ? 'success' : 'danger'" size="small">
                  {{ currentTask.status === 'completed' ? '已完成' : '失败' }}
                </el-tag>
              </div>
              <div>
                <span class="font-medium">处理时间：</span>
                <span>{{ formatDuration(currentTask.endTime?.getTime() - currentTask.startTime?.getTime() || 0) }}</span>
              </div>
              <div>
                <span class="font-medium">完成步骤：</span>
                <span>{{ currentTask.steps.filter(s => s.status === 'completed').length }}/{{ currentTask.steps.length }}</span>
              </div>
              <div>
                <span class="font-medium">使用工具：</span>
                <span>{{ getUsedTools().join(', ') || '无' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- AI响应内容 -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-3">🤖 AI响应</h3>
          <div class="bg-white dark:bg-gray-900 border rounded-lg p-4 max-h-60 overflow-y-auto">
            <pre class="whitespace-pre-wrap text-sm">{{ formatAIResponse(currentTask.result) }}</pre>
          </div>
        </div>

        <!-- 工具调用详情 -->
        <div v-if="getToolCallDetails().length > 0" class="mb-6">
          <h3 class="text-lg font-semibold mb-3">🔧 工具调用详情</h3>
          <div class="space-y-3">
            <div
              v-for="(tool, index) in getToolCallDetails()"
              :key="index"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <el-tag size="small" type="info">{{ tool.name }}</el-tag>
                  <span class="text-sm text-gray-600 dark:text-gray-400">{{ tool.server }}</span>
                </div>
                <el-tag
                  :type="tool.success ? 'success' : 'danger'"
                  size="small"
                >
                  {{ tool.success ? '成功' : '失败' }}
                </el-tag>
              </div>
              <div v-if="tool.arguments" class="mb-2">
                <div class="text-sm font-medium mb-1">参数：</div>
                <pre class="text-xs bg-gray-50 dark:bg-gray-800 p-2 rounded overflow-x-auto">{{ JSON.stringify(tool.arguments, null, 2) }}</pre>
              </div>
              <div v-if="tool.result" class="mb-2">
                <div class="text-sm font-medium mb-1">结果：</div>
                <pre class="text-xs bg-gray-50 dark:bg-gray-800 p-2 rounded overflow-x-auto">{{ JSON.stringify(tool.result, null, 2) }}</pre>
              </div>
              <div v-if="tool.error" class="text-sm text-red-600 dark:text-red-400">
                错误：{{ tool.error }}
              </div>
            </div>
          </div>
        </div>

        <!-- 处理步骤详情 -->
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-3">📊 处理步骤</h3>
          <div class="space-y-2">
            <div
              v-for="(step, index) in currentTask.steps"
              :key="step.id"
              class="flex items-center justify-between p-3 border rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <el-icon v-if="step.status === 'completed'" class="text-green-500" size="16">
                    <Check />
                  </el-icon>
                  <el-icon v-else-if="step.status === 'error'" class="text-red-500" size="16">
                    <Close />
                  </el-icon>
                  <el-icon v-else class="text-gray-400" size="16">
                    <Loading />
                  </el-icon>
                </div>
                <div>
                  <div class="font-medium">{{ step.title }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">{{ step.description }}</div>
                </div>
              </div>
              <div class="text-right text-sm">
                <div v-if="step.duration" class="text-gray-600 dark:text-gray-400">
                  {{ formatDuration(step.duration) }}
                </div>
                <div v-if="step.error" class="text-red-600 dark:text-red-400">
                  {{ step.error }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between">
          <el-button @click="copyResult" type="primary" plain>
            <el-icon><DocumentCopy /></el-icon>
            复制结果
          </el-button>
          <el-button @click="resultDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useMCPClient } from '@/composables/useMCPClient'
import { useTaskProgress } from '@/composables/useTaskProgress'
import { MCPApiClient } from '@/utils/api'
import { Check, Close, DocumentCopy, Loading, Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, ref, watch } from 'vue'
import AITaskProgress from './AITaskProgress.vue'

// 使用 MCP 客户端
const {
  isReady,
  isProcessing,
  hasError,
  lastResult,
  stats,
  availableTools,
  servers,
  history,
  error,
  processNaturalLanguage,
  retryLastRequest,
  getToolSuggestions,
  formatResult,
  refresh,
  clearError
} = useMCPClient()

// 使用任务进度管理
const {
  currentTask,
  showProgress,
  isTaskRunning,
  canCancel,
  canRetry,
  createTask,
  startTask,
  updateStepProgress,
  completeStep,
  failStep,
  cancelTask,
  retryTask,
  hideProgress,
  createAIProcessingSteps
} = useTaskProgress()

// 本地状态
const userInput = ref('')
const activeTab = ref('results')
const toolFilter = ref('')
const showError = ref(false)
const resultDialogVisible = ref(false)

// 计算属性
const suggestions = computed(() => {
  if (!userInput.value.trim()) return []
  return getToolSuggestions(userInput.value)
})

const filteredToolsByServer = computed(() => {
  if (!toolFilter.value) {
    return availableTools.value.reduce((acc, tool) => {
      if (!acc[tool.server_name]) acc[tool.server_name] = []
      acc[tool.server_name].push(tool)
      return acc
    }, {} as Record<string, typeof availableTools.value>)
  }
  
  const filtered = availableTools.value.filter(tool => 
    tool.name.toLowerCase().includes(toolFilter.value.toLowerCase()) ||
    tool.description.toLowerCase().includes(toolFilter.value.toLowerCase())
  )
  
  return filtered.reduce((acc, tool) => {
    if (!acc[tool.server_name]) acc[tool.server_name] = []
    acc[tool.server_name].push(tool)
    return acc
  }, {} as Record<string, typeof availableTools.value>)
})

// 方法
async function handleSubmit() {
  if (!userInput.value.trim() || !isReady.value) return

  const query = userInput.value.trim()

  try {
    // 创建任务进度跟踪
    const steps = createAIProcessingSteps(query)
    const task = createTask(
      '智能处理任务',
      `处理用户请求: ${query.length > 50 ? query.substring(0, 50) + '...' : query}`,
      steps
    )

    // 开始任务
    startTask(task)

    // 使用流式AI处理过程
    await processAIWithStreaming(query)

    userInput.value = ''
  } catch (err: any) {
    console.error('处理失败:', err)

    // 提取详细错误信息
    const errorMessage = err instanceof Error ? err.message : '未知错误'
    const errorDetails = err.details || ''
    const isTimeout = err.code === 'ECONNABORTED'

    // 显示用户友好的错误消息
    if (isTimeout) {
      ElMessage({
        message: `请求超时: ${errorMessage}${errorDetails ? '\n' + errorDetails : ''}`,
        type: 'warning',
        duration: 8000,
        showClose: true
      })
    } else {
      ElMessage({
        message: `处理失败: ${errorMessage}${errorDetails ? '\n' + errorDetails : ''}`,
        type: 'error',
        duration: 8000,
        showClose: true
      })
    }

    // 如果有当前任务，标记为失败
    if (currentTask.value) {
      const fullErrorMessage = errorDetails ? `${errorMessage}\n${errorDetails}` : errorMessage
      failStep(currentTask.value.currentStepIndex, fullErrorMessage)
    }
  }
}

// 流式AI处理过程
async function processAIWithStreaming(query: string) {
  if (!currentTask.value) return

  try {
    // 创建流式处理器
    const streamProcessor = MCPApiClient.createStreamProcessor()

    // 开始流式处理
    await MCPApiClient.processNaturalLanguageStream(
      { query, context: {} },
      (chunk) => handleStreamChunk(chunk, streamProcessor),
      (error) => handleStreamError(error),
      () => handleStreamComplete(streamProcessor)
    )

  } catch (error) {
    throw error
  }
}

// 处理流式数据块
function handleStreamChunk(chunk: any, processor: any) {
  if (!currentTask.value) return

  // 更新处理器状态
  processor.handleChunk(chunk)

  // 根据数据块类型更新UI
  switch (chunk.type) {
    case 'status':
      // 更新当前步骤状态
      if (chunk.step) {
        const stepIndex = getStepIndexFromName(chunk.step)
        if (stepIndex >= 0) {
          updateStepProgress(stepIndex, chunk.progress * 100, {
            description: chunk.message
          })
        }
      }
      break

    case 'content':
      // 更新AI生成的内容
      if (currentTask.value.steps[3]?.details) {
        if (chunk.delta) {
          currentTask.value.steps[3].details.aiContent += chunk.delta
        } else if (chunk.content) {
          currentTask.value.steps[3].details.aiContent = chunk.content
        }
      }
      break

    case 'tool_call_detected':
      // 工具调用检测
      updateStepProgress(1, 100, {
        description: `检测到工具调用: ${chunk.tool_name}`
      })
      break

    case 'tool_execution_start':
      // 工具开始执行
      updateStepProgress(3, chunk.progress * 100, {
        description: `正在执行工具: ${chunk.tool_name}`
      })
      break

    case 'tool_execution_complete':
      // 工具执行完成
      console.log('🔧 收到工具执行完成事件:', chunk)
      if (currentTask.value.steps[3]?.details && chunk.result) {
        if (!currentTask.value.steps[3].details.toolCalls) {
          currentTask.value.steps[3].details.toolCalls = []
        }

        const toolCall = {
          name: chunk.tool_name,
          server: chunk.server || 'minimax',
          arguments: chunk.arguments || {},
          result: chunk.result,
          success: chunk.success,
          error: chunk.error,
          duration_ms: chunk.duration_ms || 0
        }

        console.log('🔧 添加工具调用到详情:', toolCall)
        currentTask.value.steps[3].details.toolCalls.push(toolCall)
        console.log('🔧 当前工具调用列表:', currentTask.value.steps[3].details.toolCalls)
      }

      // 简化：直接设置步骤3为100%完成
      updateStepProgress(3, 100, {
        description: 'AI任务执行完成'
      })
      break

    case 'mcp_status':
      // MCP状态更新
      updateStepProgress(3, chunk.progress * 100, {
        description: chunk.message
      })
      break

    case 'response':
      // 最终响应
      if (currentTask.value.steps[3]?.details) {
        currentTask.value.steps[3].details.aiContent = chunk.content
      }
      updateStepProgress(3, chunk.progress * 100, {
        description: '处理完成'
      })
      break

    case 'complete':
      // 处理完成 - 简化版本
      console.log('🎉 处理完成')

      // 直接设置所有步骤为100%完成
      for (let i = 0; i < 5; i++) {
        currentTask.value.steps[i].status = 'completed'
        currentTask.value.steps[i].progress = 100
      }

      // 设置整体进度为100%
      currentTask.value.progress = 100
      currentTask.value.status = 'completed'
      currentTask.value.endTime = new Date()

      // 显示完成通知
      console.log('✅ 处理完成通知')

      // 重置处理状态
      isProcessing.value = false

      console.log('✅ 流式处理在complete事件中提前完成')
      break

    case 'error':
      // 处理错误
      failStep(currentTask.value.currentStepIndex, chunk.message || chunk.error)
      break
  }
}

// 处理流式错误
function handleStreamError(error: Error) {
  console.error('❌ 流式处理错误:', error)
  if (currentTask.value) {
    failStep(currentTask.value.currentStepIndex, error.message)
  }
}

// 处理流式完成
function handleStreamComplete(processor: any) {
  console.log('✅ 流式处理完成')

  if (currentTask.value) {
    // 确保所有步骤都完成
    if (currentTask.value.currentStepIndex < currentTask.value.steps.length - 1) {
      completeStep(4, {
        content: processor.content,
        toolCalls: processor.toolCalls,
        success: !processor.hasError
      })
    }
  }

  // 显示完成消息
  if (processor.hasError) {
    ElMessage.error('处理过程中出现错误')
  } else {
    ElMessage.success('处理完成')
  }
}

// 根据步骤名称获取步骤索引
function getStepIndexFromName(stepName: string): number {
  const stepMap: Record<string, number> = {
    'analyze_request': 0,
    'tool_selection': 1,
    'parameter_preparation': 2,
    'ai_execution': 3,
    'result_processing': 4
  }

  // 尝试从步骤名称中提取
  for (const [key, index] of Object.entries(stepMap)) {
    if (stepName.includes(key)) {
      return index
    }
  }

  // 默认返回当前步骤
  return currentTask.value?.currentStepIndex || 0
}

// 检测内容类型
function detectContentType(result: any): string {
  if (!result) return 'text'

  const resultStr = typeof result === 'string' ? result : JSON.stringify(result)

  if (result.image_url || result.images || resultStr.includes('.jpg') || resultStr.includes('.png')) {
    return 'image'
  }

  if (result.audio_url || result.audio || resultStr.includes('.mp3') || resultStr.includes('.wav')) {
    return 'audio'
  }

  if (result.video_url || result.video || resultStr.includes('.mp4')) {
    return 'video'
  }

  return 'text'
}

// 原有的模拟AI处理过程（保留作为备用）
async function simulateAIProcessing(query: string) {
  if (!currentTask.value) return

  try {
    // 步骤1: 分析用户需求
    updateStepProgress(0, 50, { description: '正在解析自然语言...' })
    await new Promise(resolve => setTimeout(resolve, 800))
    updateStepProgress(0, 100, { description: '需求分析完成' })
    completeStep(0, { analysis: '已识别用户意图' })

    // 步骤2: 选择合适工具
    updateStepProgress(1, 30, { description: '正在匹配最佳工具...' })
    await new Promise(resolve => setTimeout(resolve, 600))

    // 模拟工具选择逻辑
    const toolSuggestions = getToolSuggestions(query)
    const selectedToolName = toolSuggestions[0] || 'text_to_audio'
    const selectedTool = { name: selectedToolName, server_name: 'minimax' }

    updateStepProgress(1, 100, {
      description: `已选择工具: ${selectedTool.name}`,
    })

    // 更新步骤详情
    if (currentTask.value) {
      currentTask.value.steps[1].details = {
        tool: selectedTool.name,
        server: selectedTool.server_name,
        parameters: { query: query.substring(0, 100) + '...' }
      }
    }
    completeStep(1, { selectedTool })

    // 步骤3: 准备执行参数
    updateStepProgress(2, 60, { description: '正在配置执行参数...' })
    await new Promise(resolve => setTimeout(resolve, 400))
    completeStep(2, { parameters: { query, tool: selectedTool.name } })

    // 步骤4: 执行AI任务
    updateStepProgress(3, 20, { description: '正在调用AI服务...' })

    // 初始化步骤详情
    if (currentTask.value) {
      currentTask.value.steps[3].details = {
        tool: selectedTool.name,
        server: selectedTool.server_name,
        parameters: { query },
        aiContent: '',
        toolCalls: []
      }
    }

    updateStepProgress(3, 40, { description: '正在处理AI请求...' })

    // 直接调用真实的AI服务（移除硬编码模拟）
    console.log('🚀 调用真实AI服务:', query)
    const result = await processNaturalLanguage(query)
    console.log('🤖 AI Service result:', result)

    // 处理AI服务返回的真实内容
    if (currentTask.value && result) {
      updateStepProgress(3, 70, { description: '正在处理AI响应...' })

      if (currentTask.value.steps[3].details) {
        // 获取AI生成的完整内容
        let aiContent = ''
        let toolCalls = []

        if (typeof result === 'string') {
          aiContent = result
        } else if (typeof result === 'object') {
          // 处理结构化响应
          aiContent = result.response || result.content || result.text || ''
          toolCalls = result.tool_calls || result.tools_used || []

          // 处理处理步骤信息
          if (result.processing_steps) {
            console.log('📊 Processing steps:', result.processing_steps)
          }
        }

        console.log('📝 Setting real AI content:', aiContent.substring(0, 200) + '...')
        console.log('🔧 Tool calls found:', toolCalls.length)

        // 设置真实的AI内容
        currentTask.value.steps[3].details.aiContent = aiContent
        currentTask.value.steps[3].details.toolCalls = toolCalls

        // 处理多媒体内容
        if (toolCalls && toolCalls.length > 0) {
          for (const toolCall of toolCalls) {
            console.log('🔧 Processing tool call:', toolCall.name)

            // 检查是否有图片、音频或视频结果
            if (toolCall.result) {
              const result = toolCall.result

              // 处理图片结果
              if (result.image_url || result.images || (typeof result === 'string' && result.includes('.jpg') || result.includes('.png'))) {
                console.log('🖼️ Image content detected')
                toolCall.contentType = 'image'
              }

              // 处理音频结果
              if (result.audio_url || result.audio || (typeof result === 'string' && (result.includes('.mp3') || result.includes('.wav')))) {
                console.log('🎵 Audio content detected')
                toolCall.contentType = 'audio'
              }

              // 处理视频结果
              if (result.video_url || result.video || (typeof result === 'string' && result.includes('.mp4'))) {
                console.log('🎬 Video content detected')
                toolCall.contentType = 'video'
              }
            }
          }
        }

        // 如果内容很短，可能是错误信息，显示完整内容
        if (aiContent.length < 100) {
          currentTask.value.steps[3].details.showFullContent = true
        }
      }
    }

    updateStepProgress(3, 100, { description: 'AI任务执行完成' })
    completeStep(3, result)

    // 步骤5: 处理执行结果
    updateStepProgress(4, 80, { description: '正在格式化结果...' })
    await new Promise(resolve => setTimeout(resolve, 300))
    completeStep(4, result)

    ElMessage.success('处理完成')

  } catch (error) {
    throw error
  }
}

function clearInput() {
  userInput.value = ''
}

function insertTool(toolName: string) {
  userInput.value += `使用 ${toolName} 工具`
}

// 监听错误状态
watch(hasError, (newVal) => {
  showError.value = newVal
})

// 结果查看相关方法
function showResultDialog() {
  console.log('🔍 showResultDialog called')

  if (!currentTask.value) {
    console.log('❌ No current task')
    ElMessage.error('没有当前任务')
    return
  }

  console.log('📋 Current task:', currentTask.value)
  console.log('📊 Task result:', currentTask.value.result)

  // 找到执行AI任务的步骤
  const aiStep = currentTask.value.steps.find(step => step.title === '执行AI任务')
  console.log('🤖 AI Step found:', aiStep)

  if (!aiStep) {
    ElMessage.error('未找到AI执行步骤')
    return
  }

  // 确保步骤有details对象
  if (!aiStep.details) {
    aiStep.details = {}
  }

  // 处理工具调用结果
  if (currentTask.value.result && typeof currentTask.value.result === 'object') {
    console.log('📝 Processing result object:', currentTask.value.result)

    // 检查是否有工具调用数据
    if (currentTask.value.result.toolCalls && Array.isArray(currentTask.value.result.toolCalls)) {
      console.log('🔧 Found tool calls:', currentTask.value.result.toolCalls)

      // 设置工具调用数据
      aiStep.details.toolCalls = currentTask.value.result.toolCalls.map((call: any) => ({
        name: call.name || 'unknown',
        server: call.server || 'unknown',
        arguments: call.arguments || {},
        result: call.result || '',
        success: call.success !== false,
        error: call.error || '',
        duration_ms: call.duration_ms || 0
      }))

      console.log('✅ Tool calls set:', aiStep.details.toolCalls)
      ElMessage.success(`已显示 ${aiStep.details.toolCalls?.length || 0} 个工具调用结果`)
      return
    }

    // 如果没有工具调用，显示为AI内容
    let fullContent = ''
    if (currentTask.value.result.response) {
      fullContent = currentTask.value.result.response
    } else {
      fullContent = JSON.stringify(currentTask.value.result, null, 2)
    }

    console.log('📝 Full content to display:', fullContent)

    if (fullContent && fullContent.trim()) {
      aiStep.details.aiContent = fullContent
      aiStep.details.showFullContent = true
      ElMessage.success(`已展开显示完整内容 (${fullContent.length} 字符)`)
      console.log('✅ Content expanded successfully')
    } else {
      ElMessage.warning('没有找到可展开的内容')
      console.log('⚠️ No content to expand')
    }
  } else if (typeof currentTask.value.result === 'string') {
    // 处理字符串结果
    aiStep.details.aiContent = currentTask.value.result
    aiStep.details.showFullContent = true
    ElMessage.success(`已展开显示完整内容 (${currentTask.value.result.length} 字符)`)
  } else {
    ElMessage.warning('没有找到可展开的内容')
    console.log('⚠️ No content to expand')
  }
}

function formatAIResponse(result: any): string {
  if (typeof result === 'string') {
    return result
  }
  if (result && typeof result === 'object') {
    if (result.response) {
      return result.response
    }
    return JSON.stringify(result, null, 2)
  }
  return '无响应内容'
}

function getUsedTools(): string[] {
  if (!currentTask.value) return []

  // 从步骤详情中提取工具名称
  const tools = new Set<string>()
  currentTask.value.steps.forEach(step => {
    if (step.details?.tool) {
      tools.add(step.details.tool)
    }
  })

  return Array.from(tools)
}

function getToolCallDetails(): any[] {
  if (!currentTask.value) return []

  // 从当前任务的步骤中提取工具调用详情
  const toolCalls: any[] = []
  currentTask.value.steps.forEach(step => {
    if (step.details?.tool) {
      toolCalls.push({
        name: step.details.tool,
        server: step.details.server || 'unknown',
        arguments: step.details.parameters || {},
        result: step.details.result || '执行成功',
        success: step.status === 'completed',
        error: step.error || null,
        duration_ms: step.duration || 0
      })
    }
  })

  return toolCalls
}

function formatDuration(ms: number): string {
  if (!ms || ms < 0) return '0ms'
  if (ms < 1000) return `${Math.round(ms)}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  const minutes = Math.floor(ms / 60000)
  const seconds = ((ms % 60000) / 1000).toFixed(1)
  return `${minutes}m ${seconds}s`
}

async function copyResult() {
  if (!currentTask.value?.result) return

  try {
    const resultText = formatAIResponse(currentTask.value.result)
    await navigator.clipboard.writeText(resultText)
    ElMessage.success('结果已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.mcp-interface {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

:deep(.el-textarea__inner) {
  background-color: #1a1a1a;
  border-color: #444444;
  color: #ffffff;
}

:deep(.el-input__inner) {
  background-color: #1a1a1a;
  border-color: #444444;
  color: #ffffff;
}

:deep(.el-tabs__item) {
  color: #cccccc;
}

:deep(.el-tabs__item.is-active) {
  color: #ffffff;
}
</style>
