// MCP 相关类型定义
export interface MCPTool {
  name: string
  description: string
  server_name: string
  parameters?: Record<string, any>
}

export interface MCPServer {
  name: string
  connected: boolean
  tool_count: number
  tools: string[]
}

export interface MCPResponse {
  success: boolean
  result?: any
  error?: string
  provider?: string
  processing_time?: number
  tools_used?: string[]
  // 扩展属性以支持流式响应
  response?: string
  content?: string
  text?: string
  tool_calls?: any[]
  processing_steps?: any[]
  timestamp?: string
}

export interface ProcessingContext {
  user_id?: string
  session_id?: string
  previous_results?: MCPResponse[]
  preferences?: Record<string, any>
}

export interface NaturalLanguageRequest {
  query: string
  context?: ProcessingContext
  llm_provider?: 'deepseek' | 'anthropic'
}

// 服务能力类型（向后兼容）
export type ServiceCapability =
  | 'text_generation'
  | 'translation'
  | 'text_analysis'
  | 'image_generation'
  | 'speech_synthesis'
  | 'video_generation'
  | 'utility'

// 服务提供商类型
export type ServiceProvider =
  | 'deepseek'
  | 'minimax'
  | 'elevenlabs'
  | 'doubao'
  | 'vidu'
  | 'davinci-resolve'

// 健康检查响应
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy'
  mcp_servers: MCPServer[]
  total_tools: number
  uptime: number
}

// 工具调用结果
export interface ToolCallResult {
  tool_name: string
  success: boolean
  result?: any
  error?: string
  execution_time?: number
}

// 批量处理请求
export interface BatchRequest {
  requests: NaturalLanguageRequest[]
  parallel?: boolean
  max_concurrent?: number
}

// 批量处理响应
export interface BatchResponse {
  results: MCPResponse[]
  total_time: number
  success_count: number
  error_count: number
}
