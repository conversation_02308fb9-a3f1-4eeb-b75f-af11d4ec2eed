#!/usr/bin/env python3
"""
测试DeepSeek API流式端点complete事件修复
"""

import requests
import json

def test_stream_complete_fix():
    print("🧪 测试DeepSeek API流式端点complete事件修复")
    print("=" * 50)
    
    # 测试请求
    query = "请生成一张专业的TVC分镜图，内容是一个现代办公室场景"
    print(f"📤 发送流式请求: {query}")
    
    url = "http://127.0.0.1:8000/api/ai/process/stream"
    data = {"query": query}
    
    print(f"🌐 请求URL: {url}")
    print(f"📦 请求数据: {data}")
    print()
    
    try:
        response = requests.post(url, json=data, stream=True, timeout=120)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("📡 开始接收流式数据:")
            print("-" * 30)
            
            detected_tools = []
            executed_tools = []
            errors = []
            complete_received = False
            
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data: '):
                    data_str = line[6:]  # 移除 'data: ' 前缀
                    
                    if data_str == '[DONE]':
                        print("🏁 收到结束标识 [DONE]")
                        break
                    
                    try:
                        chunk = json.loads(data_str)
                        chunk_type = chunk.get('type', 'unknown')
                        
                        if chunk_type == 'status':
                            print(f"📊 状态: {chunk.get('message', 'N/A')}")
                        
                        elif chunk_type == 'tool_call_detected':
                            tool_name = chunk.get('tool_name', 'unknown')
                            detected_tools.append(tool_name)
                            print(f"🔧 检测到工具调用: {tool_name}")
                        
                        elif chunk_type == 'tool_execution_start':
                            tool_name = chunk.get('tool_name', 'unknown')
                            params = chunk.get('parameters', {})
                            print(f"⚡ 开始执行工具: {tool_name}")
                            print(f"   参数: {params}")
                        
                        elif chunk_type == 'tool_execution_complete':
                            tool_name = chunk.get('tool_name', 'unknown')
                            success = chunk.get('success', False)
                            result = chunk.get('result', 'N/A')
                            executed_tools.append(tool_name)
                            print(f"✅ 工具执行完成: {tool_name}")
                            print(f"   结果: {str(result)[:100]}...")
                        
                        elif chunk_type == 'complete':
                            complete_received = True
                            print(f"🎉 收到完成事件: {chunk.get('message', 'N/A')}")
                            print(f"   进度: {chunk.get('progress', 0) * 100}%")
                        
                        elif chunk_type == 'error':
                            error_msg = chunk.get('message', chunk.get('error', 'Unknown error'))
                            errors.append(error_msg)
                            print(f"❌ 错误: {error_msg}")
                        
                        elif chunk_type == 'mcp_status':
                            # 简化MCP状态输出
                            print(f"📦 其他数据: {chunk_type} - {chunk}")
                        
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON解析错误: {e}")
                        print(f"   原始数据: {data_str}")
            
            print("🏁 流式响应完成")
            print()
            print("=" * 50)
            print("📊 测试结果汇总:")
            print(f"🔧 检测到的工具调用: {detected_tools}")
            print(f"✅ 成功执行的工具: {executed_tools}")
            print(f"❌ 错误数量: {len(errors)}")
            print(f"🎉 是否收到complete事件: {'是' if complete_received else '否'}")
            print()
            
            if complete_received:
                print("🎉 流式端点complete事件修复成功！")
            else:
                print("❌ 流式端点仍未发送complete事件")
                
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_stream_complete_fix()
